import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'tenant_config.g.dart';

@JsonSerializable()
class TenantConfig extends Equatable {
  final String id;
  final String name;
  final String displayName;
  final TenantTheme theme;
  final TenantAssets assets;
  final TenantLayout layout;
  final Map<String, dynamic> customSettings;
  final bool isActive;
  final DateTime? lastUpdated;

  const TenantConfig({
    required this.id,
    required this.name,
    required this.displayName,
    required this.theme,
    required this.assets,
    required this.layout,
    this.customSettings = const {},
    this.isActive = true,
    this.lastUpdated,
  });

  factory TenantConfig.fromJson(Map<String, dynamic> json) =>
      _$TenantConfigFromJson(json);

  Map<String, dynamic> toJson() => _$TenantConfigToJson(this);

  @override
  List<Object?> get props => [
    id,
    name,
    displayName,
    theme,
    assets,
    layout,
    customSettings,
    isActive,
    lastUpdated,
  ];

  TenantConfig copyWith({
    String? id,
    String? name,
    String? displayName,
    TenantTheme? theme,
    TenantAssets? assets,
    TenantLayout? layout,
    Map<String, dynamic>? customSettings,
    bool? isActive,
    DateTime? lastUpdated,
  }) {
    return TenantConfig(
      id: id ?? this.id,
      name: name ?? this.name,
      displayName: displayName ?? this.displayName,
      theme: theme ?? this.theme,
      assets: assets ?? this.assets,
      layout: layout ?? this.layout,
      customSettings: customSettings ?? this.customSettings,
      isActive: isActive ?? this.isActive,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }
}

@JsonSerializable()
class TenantTheme extends Equatable {
  final TenantColorScheme primaryColors;
  final TenantColorScheme? darkColors;
  final String? fontFamily;
  final Map<String, dynamic> customStyles;

  const TenantTheme({
    required this.primaryColors,
    this.darkColors,
    this.fontFamily,
    this.customStyles = const {},
  });

  factory TenantTheme.fromJson(Map<String, dynamic> json) =>
      _$TenantThemeFromJson(json);

  Map<String, dynamic> toJson() => _$TenantThemeToJson(this);

  @override
  List<Object?> get props => [
    primaryColors,
    darkColors,
    fontFamily,
    customStyles,
  ];
}

@JsonSerializable()
class TenantColorScheme extends Equatable {
  final String primary;
  final String? secondary;
  final String? surface;
  final String? background;
  final String? error;
  final String? onPrimary;
  final String? onSecondary;
  final String? onSurface;
  final String? onBackground;
  final String? onError;

  const TenantColorScheme({
    required this.primary,
    this.secondary,
    this.surface,
    this.background,
    this.error,
    this.onPrimary,
    this.onSecondary,
    this.onSurface,
    this.onBackground,
    this.onError,
  });

  factory TenantColorScheme.fromJson(Map<String, dynamic> json) =>
      _$TenantColorSchemeFromJson(json);

  Map<String, dynamic> toJson() => _$TenantColorSchemeToJson(this);

  @override
  List<Object?> get props => [
    primary,
    secondary,
    surface,
    background,
    error,
    onPrimary,
    onSecondary,
    onSurface,
    onBackground,
    onError,
  ];
}

@JsonSerializable()
class TenantAssets extends Equatable {
  final String? logo;
  final String? logoLight;
  final String? logoDark;
  final String? favicon;
  final String? splashImage;
  final Map<String, String> customImages;

  const TenantAssets({
    this.logo,
    this.logoLight,
    this.logoDark,
    this.favicon,
    this.splashImage,
    this.customImages = const {},
  });

  factory TenantAssets.fromJson(Map<String, dynamic> json) =>
      _$TenantAssetsFromJson(json);

  Map<String, dynamic> toJson() => _$TenantAssetsToJson(this);

  @override
  List<Object?> get props => [
    logo,
    logoLight,
    logoDark,
    favicon,
    splashImage,
    customImages,
  ];
}

@JsonSerializable()
class TenantLayout extends Equatable {
  final String layoutType; // 'standard', 'compact', 'sidebar', 'cards'
  final bool showHeader;
  final bool showSidebar;
  final bool showFooter;
  final String appBarStyle; // 'standard', 'compact', 'prominent'
  final String navigationStyle; // 'bottom', 'rail', 'drawer'
  final String cardStyle; // 'elevated', 'outlined', 'filled'
  final Map<String, dynamic> componentSettings;
  final List<String> hiddenFeatures;
  final Map<String, int> spacing; // Custom spacing values

  const TenantLayout({
    this.layoutType = 'standard',
    this.showHeader = true,
    this.showSidebar = false,
    this.showFooter = true,
    this.appBarStyle = 'standard',
    this.navigationStyle = 'bottom',
    this.cardStyle = 'elevated',
    this.componentSettings = const {},
    this.hiddenFeatures = const [],
    this.spacing = const {},
  });

  factory TenantLayout.fromJson(Map<String, dynamic> json) =>
      _$TenantLayoutFromJson(json);

  Map<String, dynamic> toJson() => _$TenantLayoutToJson(this);

  @override
  List<Object?> get props => [
    layoutType,
    showHeader,
    showSidebar,
    showFooter,
    appBarStyle,
    navigationStyle,
    cardStyle,
    componentSettings,
    hiddenFeatures,
    spacing,
  ];

  static defaultLayout() {}
}
