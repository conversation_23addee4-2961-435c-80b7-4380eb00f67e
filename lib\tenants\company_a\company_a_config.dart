import '../../features/tenant/domain/entities/tenant_config.dart';

/// Company A tenant configuration - Compact business layout
class CompanyATenantConfig {
  static const TenantConfig config = TenantConfig(
    id: 'company_a',
    name: 'company_a',
    displayName: 'Company A',
    theme: TenantTheme(
      primaryColors: TenantColorScheme(
        primary: '#E91E63',
        secondary: '#FF5722',
        surface: '#FFFFFF',
        background: '#FAFAFA',
        error: '#F44336',
        onPrimary: '#FFFFFF',
        onSecondary: '#FFFFFF',
        onSurface: '#000000',
        onBackground: '#000000',
        onError: '#FFFFFF',
      ),
      fontFamily: 'Montserrat',
    ),
    assets: TenantAssets(
      logo: 'assets/images/tenants/company_a/company_a_logo.png',
      logoLight: 'assets/images/tenants/company_a/company_a_logo_light.png',
      logoDark: 'assets/images/tenants/company_a/company_a_logo_dark.png',
      customImages: {
        'hero_banner': 'assets/images/tenants/company_a/hero_banner.jpg',
        'feature_image': 'assets/images/tenants/company_a/feature.jpg',
        'office_image': 'assets/images/tenants/company_a/office.jpg',
        'team_image': 'assets/images/tenants/company_a/team.jpg',
      },
    ),
    layout: TenantLayout(
      layoutType: 'compact',
      showHeader: true,
      showSidebar: false,
      showFooter: true,
      appBarStyle: 'compact',
      navigationStyle: 'bottom',
      cardStyle: 'outlined',
      componentSettings: {
        'cardElevation': 2,
        'borderRadius': 8,
        'compactMode': true,
      },
      spacing: {'small': 8, 'medium': 16, 'large': 24},
    ),
    customSettings: {
      'companyType': 'business',
      'industry': 'corporate',
      'supportEmail': '<EMAIL>',
      'features': ['business_features', 'compact_ui', 'analytics'],
      'brandColors': ['#E91E63', '#FF5722'],
      'motto': 'Excellence in Business',
    },
  );
}
