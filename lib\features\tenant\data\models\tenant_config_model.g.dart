// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'tenant_config_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TenantConfigModel _$TenantConfigModelFromJson(Map<String, dynamic> json) =>
    TenantConfigModel(
      id: json['id'] as String,
      name: json['name'] as String,
      displayName: json['displayName'] as String,
      theme: TenantTheme.fromJson(json['theme'] as Map<String, dynamic>),
      assets: TenantAssets.fromJson(json['assets'] as Map<String, dynamic>),
      customSettings:
          json['customSettings'] as Map<String, dynamic>? ?? const {},
      isActive: json['isActive'] as bool? ?? true,
      lastUpdated: json['lastUpdated'] == null
          ? null
          : DateTime.parse(json['lastUpdated'] as String),
      layout: json['layout'] == null
          ? TenantLayout.defaultLayout()
          : TenantLayout.fromJson(json['layout'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$TenantConfigModelToJson(TenantConfigModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'displayName': instance.displayName,
      'theme': instance.theme,
      'assets': instance.assets,
      'customSettings': instance.customSettings,
      'isActive': instance.isActive,
      'lastUpdated': instance.lastUpdated?.toIso8601String(),
    };
