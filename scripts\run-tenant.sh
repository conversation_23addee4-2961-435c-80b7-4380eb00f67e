#!/bin/bash
# Quick run script for development with tenant selection
# Usage: ./run-tenant.sh [tenant-id] [device]
# Example: ./run-tenant.sh ocigm chrome

# Default values
TENANT_ID="${1:-default}"
DEVICE="${2:-chrome}"

# Available tenants
AVAILABLE_TENANTS="default company_a company_b ocigm"

# Function to check if value is in list
contains() {
    local seeking=$1; shift
    local in=1
    for element; do
        if [[ $element == "$seeking" ]]; then
            in=0
            break
        fi
    done
    return $in
}

# Validate tenant ID
if ! contains "$TENANT_ID" $AVAILABLE_TENANTS; then
    echo "Error: Invalid tenant ID '$TENANT_ID'"
    echo "Available tenants: $AVAILABLE_TENANTS"
    exit 1
fi

echo "============================================"
echo "Running Multi-Tenant Flutter App"
echo "============================================"
echo "Tenant: $TENANT_ID"
echo "Device: $DEVICE"
echo "============================================"

# Check for Android device and handle emulator
if [[ "$DEVICE" == "android" ]]; then
    echo "Checking for Android devices..."
    if ! flutter devices | grep -q "android"; then
        echo "No Android devices found. Checking available emulators..."
        flutter emulators
        echo ""
        echo "Please start an Android emulator first using:"
        echo "  flutter emulators --launch <emulator_name>"
        echo ""
        echo "Then run this script again."
        exit 1
    fi
fi

# Run the app with tenant configuration
flutter run -d "$DEVICE" --dart-define=TENANT_ID="$TENANT_ID"
