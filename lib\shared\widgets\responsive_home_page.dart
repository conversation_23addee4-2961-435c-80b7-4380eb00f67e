import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../features/tenant/presentation/bloc/tenant_bloc.dart';
import '../../features/tenant/domain/entities/tenant_config.dart';
import 'responsive_scaffold.dart';
import 'user_info_card.dart';
import '../services/tenant_asset_service.dart';
import '../localization/app_localizations.dart';
import '../../tenants/company_a/company_a_widgets.dart';
import '../../tenants/company_b/company_b_widgets.dart';
import '../../tenants/ocigm/ocigm_widgets.dart';
import '../../tenants/default/default_widgets.dart';

/// Responsive home page that adapts navigation based on screen size
class ResponsiveHomePage extends StatefulWidget {
  const ResponsiveHomePage({super.key});

  @override
  State<ResponsiveHomePage> createState() => _ResponsiveHomePageState();
}

class _ResponsiveHomePageState extends State<ResponsiveHomePage> {
  int _currentIndex = 0;

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<TenantBloc, TenantState>(
      builder: (context, state) {
        if (state is TenantLoading) {
          return _buildLoadingScaffold(context);
        }

        if (state is TenantError) {
          return _buildErrorScaffold(context, state.message);
        }

        if (state is TenantLoaded) {
          return _buildResponsiveScaffold(context, state.config);
        }

        return _buildLoadingScaffold(context);
      },
    );
  }

  Widget _buildLoadingScaffold(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text(context.t('app_title', 'Multi-Tenant App'))),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const CircularProgressIndicator(),
            const SizedBox(height: 16),
            Text(context.t('loading', 'Loading...')),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorScaffold(BuildContext context, String message) {
    return Scaffold(
      appBar: AppBar(title: Text(context.t('app_title', 'Multi-Tenant App'))),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Theme.of(context).colorScheme.error,
            ),
            const SizedBox(height: 16),
            Text(
              context.t('error', 'Error'),
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(message),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                context.read<TenantBloc>().add(
                  const LoadTenantConfig('default'),
                );
              },
              child: Text(context.t('retry', 'Retry')),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildResponsiveScaffold(BuildContext context, TenantConfig config) {
    return ResponsiveScaffold(
      tenantConfig: config,
      title: _getPageTitle(config),
      currentIndex: _currentIndex,
      onNavigationChanged: (index) {
        setState(() {
          _currentIndex = index;
        });
        _handleNavigation(context, index, config);
      },
      body: _buildPageContent(context, config),
      floatingActionButton: _buildFloatingActionButton(context, config),
    );
  }

  String _getPageTitle(TenantConfig config) {
    switch (_currentIndex) {
      case 0:
        return 'Home';
      case 1:
        return 'Dashboard';
      case 2:
        return _getTenantSpecificPageTitle(config, 2);
      case 3:
        return _getTenantSpecificPageTitle(config, 3);
      case 4:
        return _getTenantSpecificPageTitle(config, 4);
      default:
        return config.displayName;
    }
  }

  String _getTenantSpecificPageTitle(TenantConfig config, int index) {
    switch (config.id) {
      case 'company_a':
        switch (index) {
          case 2:
            return 'Projects';
          case 3:
            return 'Clients';
          case 4:
            return 'Reports';
          default:
            return 'Business';
        }
      case 'company_b':
        switch (index) {
          case 2:
            return 'Sustainability';
          case 3:
            return 'Eco Projects';
          case 4:
            return 'Impact';
          default:
            return 'EcoTech';
        }
      case 'ocigm':
        switch (index) {
          case 2:
            return 'Research';
          case 3:
            return 'Lab';
          case 4:
            return 'Publications';
          default:
            return 'Innovation';
        }
      default:
        return 'Settings';
    }
  }

  Widget _buildPageContent(BuildContext context, TenantConfig config) {
    switch (_currentIndex) {
      case 0:
        return _buildHomePage(context, config);
      case 1:
        return _buildDashboardPage(context, config);
      default:
        return _buildTenantSpecificPage(context, config, _currentIndex);
    }
  }

  Widget _buildHomePage(BuildContext context, TenantConfig config) {
    // Use tenant-specific home pages
    switch (config.id) {
      case 'company_a':
        return CompanyAWidgets.buildHomePage(context, config);
      case 'company_b':
        return CompanyBWidgets.buildHomePage(context, config);
      case 'ocigm':
        return OCIGMWidgets.buildHomePage(context, config);
      default:
        return DefaultWidgets.buildHomePage(context, config);
    }
  }

  Widget _buildDashboardPage(BuildContext context, TenantConfig config) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('Dashboard', style: Theme.of(context).textTheme.headlineMedium),
          const SizedBox(height: 24),

          // Tenant-specific dashboard content
          if (config.id == 'company_a')
            CompanyAWidgets.buildBusinessMetrics(context, config)
          else if (config.id == 'ocigm')
            OCIGMWidgets.buildInnovationDashboard(context, config)
          else
            _buildGenericDashboard(context, config),
        ],
      ),
    );
  }

  Widget _buildTenantSpecificPage(
    BuildContext context,
    TenantConfig config,
    int index,
  ) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.construction,
            size: 64,
            color: Theme.of(context).colorScheme.primary,
          ),
          const SizedBox(height: 16),
          Text(
            'Page Under Construction',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 8),
          Text(
            'This ${_getTenantSpecificPageTitle(config, index)} page is coming soon!',
            style: Theme.of(context).textTheme.bodyLarge,
          ),
        ],
      ),
    );
  }

  Widget _buildWelcomeSection(BuildContext context, TenantConfig config) {
    return TenantAwareCard(
      tenantConfig: config,
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Welcome to ${config.displayName}',
              style: Theme.of(
                context,
              ).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Text(
              config.customSettings['customWelcomeMessage'] ??
                  'Welcome to our application!',
              style: Theme.of(context).textTheme.bodyLarge,
            ),
            const SizedBox(height: 16),
            _buildQuickActions(context, config),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActions(BuildContext context, TenantConfig config) {
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: [
        _buildActionChip(context, 'Dashboard', Icons.dashboard, () {
          setState(() => _currentIndex = 1);
        }),
        _buildActionChip(context, 'Profile', Icons.person, () {
          _showFeatureDialog(context, 'Profile', 'View your profile');
        }),
        _buildActionChip(context, 'Settings', Icons.settings, () {
          _showFeatureDialog(context, 'Settings', 'Application settings');
        }),
      ],
    );
  }

  Widget _buildActionChip(
    BuildContext context,
    String label,
    IconData icon,
    VoidCallback onTap,
  ) {
    return ActionChip(
      avatar: Icon(icon, size: 18),
      label: Text(label),
      onPressed: onTap,
    );
  }

  Widget _buildTenantSpecificContent(
    BuildContext context,
    TenantConfig config,
  ) {
    switch (config.id) {
      case 'company_a':
        return CompanyAWidgets.buildDashboardHeader(context, config);
      case 'ocigm':
        return OCIGMWidgets.buildPremiumWelcome(context, config);
      default:
        return _buildGenericContent(context, config);
    }
  }

  Widget _buildGenericContent(BuildContext context, TenantConfig config) {
    return TenantAwareCard(
      tenantConfig: config,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Features', style: Theme.of(context).textTheme.titleLarge),
            const SizedBox(height: 16),
            _buildFeatureList(context, config),
          ],
        ),
      ),
    );
  }

  Widget _buildGenericDashboard(BuildContext context, TenantConfig config) {
    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: MediaQuery.of(context).size.width > 600 ? 3 : 2,
      mainAxisSpacing: 16,
      crossAxisSpacing: 16,
      children: [
        _buildDashboardCard(context, 'Users', '1,234', Icons.people),
        _buildDashboardCard(context, 'Projects', '45', Icons.work),
        _buildDashboardCard(context, 'Tasks', '123', Icons.task),
        _buildDashboardCard(context, 'Reports', '67', Icons.analytics),
      ],
    );
  }

  Widget _buildDashboardCard(
    BuildContext context,
    String title,
    String value,
    IconData icon,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, size: 32, color: Theme.of(context).colorScheme.primary),
            const SizedBox(height: 8),
            Text(
              value,
              style: Theme.of(
                context,
              ).textTheme.headlineMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            Text(title, style: Theme.of(context).textTheme.bodyMedium),
          ],
        ),
      ),
    );
  }

  Widget _buildFeatureList(BuildContext context, TenantConfig config) {
    final features = [
      'Multi-tenant architecture',
      'Responsive design',
      'Custom themes',
      'Internationalization',
    ];

    return Column(
      children: features
          .map(
            (feature) => ListTile(
              leading: Icon(
                Icons.check_circle,
                color: Theme.of(context).colorScheme.primary,
              ),
              title: Text(feature),
              dense: true,
            ),
          )
          .toList(),
    );
  }

  Widget? _buildFloatingActionButton(
    BuildContext context,
    TenantConfig config,
  ) {
    // Show FAB only on home page
    if (_currentIndex != 0) return null;

    return FloatingActionButton(
      onPressed: () {
        _showFeatureDialog(
          context,
          'Quick Action',
          'This is a tenant-specific quick action for ${config.displayName}',
        );
      },
      child: const Icon(Icons.add),
    );
  }

  void _handleNavigation(BuildContext context, int index, TenantConfig config) {
    // Handle navigation logic here
    // For now, just update the current index (already done in onNavigationChanged)

    // In a real app, you might want to use a router like GoRouter
    // or push new pages based on the selected navigation item
  }

  void _showFeatureDialog(
    BuildContext context,
    String title,
    String description,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(description),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}
