import '../models/tenant_config_model.dart';
import '../../../../core/network/api_client.dart';
import '../../../../core/error/exceptions.dart';

abstract class TenantRemoteDataSource {
  Future<TenantConfigModel> getTenantConfig(String tenantId);
  Future<List<TenantConfigModel>> getAvailableTenants();
}

class TenantRemoteDataSourceImpl implements TenantRemoteDataSource {
  final ApiClient apiClient;

  TenantRemoteDataSourceImpl(this.apiClient);

  @override
  Future<TenantConfigModel> getTenantConfig(String tenantId) async {
    try {
      final response = await apiClient.getTenantConfig(tenantId);
      return TenantConfigModel.fromJson(response);
    } catch (e) {
      throw ServerException('Failed to fetch tenant config: $e');
    }
  }

  @override
  Future<List<TenantConfigModel>> getAvailableTenants() async {
    try {
      final response = await apiClient.getTenants();
      return response.map((json) => TenantConfigModel.fromJson(json)).toList();
    } catch (e) {
      throw ServerException('Failed to fetch available tenants: $e');
    }
  }
}
