import 'package:dartz/dartz.dart';
import '../../domain/entities/tenant_config.dart';
import '../../domain/repositories/tenant_repository.dart';
import '../datasources/tenant_local_data_source.dart';
import '../datasources/tenant_remote_data_source.dart';
import '../models/tenant_config_model.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/error/exceptions.dart';

class TenantRepositoryImpl implements TenantRepository {
  final TenantLocalDataSource localDataSource;
  final TenantRemoteDataSource remoteDataSource;

  TenantRepositoryImpl({
    required this.localDataSource,
    required this.remoteDataSource,
  });

  @override
  Future<Either<Failure, TenantConfig>> getTenantConfig(String tenantId) async {
    try {
      // Try to get from cache first
      final cachedConfig = await localDataSource.getCachedTenantConfig(
        tenantId,
      );
      if (cachedConfig != null) {
        return Right(cachedConfig);
      }

      // Fetch from remote
      final remoteConfig = await remoteDataSource.getTenantConfig(tenantId);

      // Cache the result
      await localDataSource.cacheTenantConfig(remoteConfig);

      return Right(remoteConfig);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    } on CacheException catch (e) {
      return Left(CacheFailure(e.message));
    } catch (e) {
      return Left(ServerFailure('Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, List<TenantConfig>>> getAvailableTenants() async {
    try {
      final tenants = await remoteDataSource.getAvailableTenants();
      return Right(tenants.cast<TenantConfig>());
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    } catch (e) {
      return Left(ServerFailure('Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> switchTenant(String tenantId) async {
    try {
      // Get the tenant config first
      final configResult = await getTenantConfig(tenantId);

      return configResult.fold((failure) => Left(failure), (config) async {
        try {
          await localDataSource.setCurrentTenantId(tenantId);
          return const Right(null);
        } on CacheException catch (e) {
          return Left(CacheFailure(e.message));
        }
      });
    } catch (e) {
      return Left(ServerFailure('Failed to switch tenant: $e'));
    }
  }

  @override
  Future<Either<Failure, TenantConfig?>> getCurrentTenant() async {
    try {
      final currentTenantId = await localDataSource.getCurrentTenantId();
      if (currentTenantId == null) {
        return const Right(null);
      }

      final configResult = await getTenantConfig(currentTenantId);
      return configResult.fold(
        (failure) => Left(failure),
        (config) => Right(config),
      );
    } on CacheException catch (e) {
      return Left(CacheFailure(e.message));
    } catch (e) {
      return Left(ServerFailure('Failed to get current tenant: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> cacheTenantConfig(TenantConfig config) async {
    try {
      final model = TenantConfigModel.fromEntity(config);
      await localDataSource.cacheTenantConfig(model);
      return const Right(null);
    } on CacheException catch (e) {
      return Left(CacheFailure(e.message));
    } catch (e) {
      return Left(CacheFailure('Failed to cache tenant config: $e'));
    }
  }
}
