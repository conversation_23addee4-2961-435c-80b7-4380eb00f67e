import 'package:json_annotation/json_annotation.dart';
import '../../domain/entities/tenant_config.dart';

part 'tenant_config_model.g.dart';

@JsonSerializable()
class TenantConfigModel extends TenantConfig {
  const TenantConfigModel({
    required super.id,
    required super.name,
    required super.displayName,
    required super.theme,
    required super.assets,
    super.customSettings = const {},
    super.isActive = true,
    super.lastUpdated, required super.layout,
  });

  factory TenantConfigModel.fromJson(Map<String, dynamic> json) =>
      _$TenantConfigModelFromJson(json);

  @override
  Map<String, dynamic> toJson() => _$TenantConfigModelToJson(this);

  factory TenantConfigModel.fromEntity(TenantConfig entity) {
    return TenantConfigModel(
      id: entity.id,
      name: entity.name,
      displayName: entity.displayName,
      theme: entity.theme,
      assets: entity.assets,
      customSettings: entity.customSettings,
      isActive: entity.isActive,
      lastUpdated: entity.lastUpdated, layout: entity.layout,
    );
  }
}
