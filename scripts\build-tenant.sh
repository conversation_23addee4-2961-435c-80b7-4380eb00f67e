#!/bin/bash
# Multi-Tenant Flutter Build Script
# Usage: ./build-tenant.sh [tenant-id] [platform] [build-type]
# Example: ./build-tenant.sh ocigm web release

# Default values
TENANT_ID="${1:-default}"
PLATFORM="${2:-web}"
BUILD_TYPE="${3:-release}"

# Available options
AVAILABLE_TENANTS="default company_a company_b ocigm"
AVAILABLE_PLATFORMS="web apk appbundle windows macos ios"

# Function to check if value is in list
contains() {
    local seeking=$1; shift
    local in=1
    for element; do
        if [[ $element == "$seeking" ]]; then
            in=0
            break
        fi
    done
    return $in
}

# Validate tenant ID
if ! contains "$TENANT_ID" $AVAILABLE_TENANTS; then
    echo "Error: Invalid tenant ID '$TENANT_ID'"
    echo "Available tenants: $AVAILABLE_TENANTS"
    exit 1
fi

# Validate platform
if ! contains "$PLATFORM" $AVAILABLE_PLATFORMS; then
    echo "Error: Invalid platform '$PLATFORM'"
    echo "Available platforms: $AVAILABLE_PLATFORMS"
    exit 1
fi

echo "============================================"
echo "Building Multi-Tenant Flutter App"
echo "============================================"
echo "Tenant: $TENANT_ID"
echo "Platform: $PLATFORM"
echo "Build Type: $BUILD_TYPE"
echo "============================================"

# Set build command based on platform
case $PLATFORM in
    "web")
        BUILD_CMD="flutter build web --dart-define=TENANT_ID=$TENANT_ID --$BUILD_TYPE"
        ;;
    "apk")
        BUILD_CMD="flutter build apk --dart-define=TENANT_ID=$TENANT_ID --$BUILD_TYPE"
        ;;
    "appbundle")
        BUILD_CMD="flutter build appbundle --dart-define=TENANT_ID=$TENANT_ID --$BUILD_TYPE"
        ;;
    "windows")
        BUILD_CMD="flutter build windows --dart-define=TENANT_ID=$TENANT_ID --$BUILD_TYPE"
        ;;
    "macos")
        BUILD_CMD="flutter build macos --dart-define=TENANT_ID=$TENANT_ID --$BUILD_TYPE"
        ;;
    "ios")
        BUILD_CMD="flutter build ios --dart-define=TENANT_ID=$TENANT_ID --$BUILD_TYPE"
        ;;
    *)
        echo "Error: Unsupported platform '$PLATFORM'"
        exit 1
        ;;
esac

echo "Executing: $BUILD_CMD"
echo

# Execute the build command
eval $BUILD_CMD

if [ $? -eq 0 ]; then
    echo
    echo "============================================"
    echo "Build completed successfully!"
    echo "============================================"
    echo "Tenant: $TENANT_ID"
    echo "Platform: $PLATFORM"
    echo "Build Type: $BUILD_TYPE"
    
    case $PLATFORM in
        "web")
            echo
            echo "Web build output: build/web/"
            echo "To serve locally: flutter run -d chrome --dart-define=TENANT_ID=$TENANT_ID"
            ;;
        "windows")
            echo
            echo "Windows build output: build/windows/x64/runner/$BUILD_TYPE/"
            ;;
        "macos")
            echo
            echo "macOS build output: build/macos/Build/Products/$BUILD_TYPE/"
            ;;
        "ios")
            echo
            echo "iOS build output: build/ios/Release-iphoneos/"
            ;;
    esac
    echo "============================================"
else
    echo
    echo "============================================"
    echo "Build failed with error code $?"
    echo "============================================"
    exit 1
fi
