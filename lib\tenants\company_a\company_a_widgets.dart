import 'package:flutter/material.dart';
import '../../features/tenant/domain/entities/tenant_config.dart';
import '../../shared/widgets/user_info_card.dart';

/// Custom widgets specific to Company A tenant
class CompanyAWidgets {
  /// Company A specific dashboard header
  static Widget buildDashboardHeader(
    BuildContext context,
    TenantConfig config,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            _parseColor(config.theme.primaryColors.primary),
            _parseColor(
              config.theme.primaryColors.secondary ??
                  config.theme.primaryColors.primary,
            ),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.business_center, color: Colors.white, size: 32),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Business Dashboard',
                      style: Theme.of(context).textTheme.headlineSmall
                          ?.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                    ),
                    Text(
                      config.customSettings['motto'] ??
                          'Excellence in Business',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Colors.white.withOpacity(0.9),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildQuickStats(context),
        ],
      ),
    );
  }

  /// Company A specific business metrics card
  static Widget buildBusinessMetrics(
    BuildContext context,
    TenantConfig config,
  ) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
        side: BorderSide(
          color: _parseColor(
            config.theme.primaryColors.primary,
          ).withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Business Metrics',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildMetricItem(
                    context,
                    'Revenue',
                    '₹2.5M',
                    Icons.trending_up,
                  ),
                ),
                Expanded(
                  child: _buildMetricItem(
                    context,
                    'Clients',
                    '148',
                    Icons.people,
                  ),
                ),
                Expanded(
                  child: _buildMetricItem(
                    context,
                    'Projects',
                    '23',
                    Icons.work,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// Company A specific user profile section
  static Widget buildUserProfile(
    BuildContext context,
    TenantConfig config, {
    bool isCompact = false,
  }) {
    return CurrentUserInfoCard(
      tenantConfig: config,
      isCompact: isCompact,
      onProfileTap: () => _showFeatureDialog(
        context,
        'User Profile',
        'View and manage your profile information',
      ),
      onSettingsTap: () => _showFeatureDialog(
        context,
        'Account Settings',
        'Update your account preferences and settings',
      ),
    );
  }

  /// Compact action buttons for Company A
  static Widget buildCompactActions(BuildContext context, TenantConfig config) {
    final primaryColor = _parseColor(config.theme.primaryColors.primary);

    return Row(
      children: [
        Expanded(
          child: OutlinedButton.icon(
            onPressed: () => _showFeatureDialog(
              context,
              'Reports',
              'Business analytics and reports',
            ),
            icon: Icon(Icons.analytics, size: 16),
            label: Text('Reports'),
            style: OutlinedButton.styleFrom(
              foregroundColor: primaryColor,
              side: BorderSide(color: primaryColor),
            ),
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: OutlinedButton.icon(
            onPressed: () => _showFeatureDialog(
              context,
              'Clients',
              'Client management system',
            ),
            icon: Icon(Icons.people, size: 16),
            label: Text('Clients'),
            style: OutlinedButton.styleFrom(
              foregroundColor: primaryColor,
              side: BorderSide(color: primaryColor),
            ),
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: ElevatedButton.icon(
            onPressed: () => _showFeatureDialog(
              context,
              'New Project',
              'Create new business project',
            ),
            icon: Icon(Icons.add, size: 16),
            label: Text('Add'),
            style: ElevatedButton.styleFrom(
              backgroundColor: primaryColor,
              foregroundColor: Colors.white,
            ),
          ),
        ),
      ],
    );
  }

  static Widget _buildQuickStats(BuildContext context) {
    return Row(
      children: [
        Expanded(child: _buildStatItem(context, '24', 'Active Projects')),
        Expanded(child: _buildStatItem(context, '156', 'Team Members')),
        Expanded(child: _buildStatItem(context, '98%', 'Client Satisfaction')),
      ],
    );
  }

  static Widget _buildStatItem(
    BuildContext context,
    String value,
    String label,
  ) {
    return Column(
      children: [
        Text(
          value,
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: Theme.of(
            context,
          ).textTheme.bodySmall?.copyWith(color: Colors.white.withOpacity(0.8)),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  static Widget _buildMetricItem(
    BuildContext context,
    String label,
    String value,
    IconData icon,
  ) {
    return Column(
      children: [
        Icon(icon, color: Theme.of(context).primaryColor),
        const SizedBox(height: 8),
        Text(
          value,
          style: Theme.of(
            context,
          ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
        ),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall,
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  static Color _parseColor(String colorString) {
    return Color(int.parse(colorString.replaceFirst('#', '0xFF')));
  }

  static void _showFeatureDialog(
    BuildContext context,
    String title,
    String description,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(description),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  /// Company A specific home page content
  static Widget buildHomePage(BuildContext context, TenantConfig config) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // User info section
          CurrentUserInfoCard(
            tenantConfig: config,
            isCompact: false,
            onProfileTap: () => _showFeatureDialog(
              context,
              'User Profile',
              'View and edit your business profile information',
            ),
            onSettingsTap: () => _showFeatureDialog(
              context,
              'Business Settings',
              'Manage your business account settings',
            ),
          ),
          const SizedBox(height: 24),

          // Business Welcome Section
          _buildBusinessWelcomeSection(context, config),
          const SizedBox(height: 24),

          // Business Metrics Overview
          _buildBusinessMetricsOverview(context, config),
          const SizedBox(height: 24),

          // Recent Projects Section
          _buildRecentProjectsSection(context, config),
          const SizedBox(height: 24),

          // Quick Business Actions
          _buildBusinessQuickActions(context, config),
        ],
      ),
    );
  }

  /// Business-specific welcome section
  static Widget _buildBusinessWelcomeSection(
    BuildContext context,
    TenantConfig config,
  ) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            _parseColor(config.theme.primaryColors.primary),
            _parseColor(
              config.theme.primaryColors.secondary ??
                  config.theme.primaryColors.primary,
            ),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.business_center, color: Colors.white, size: 32),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Welcome to ${config.displayName}',
                      style: Theme.of(context).textTheme.headlineSmall
                          ?.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                    ),
                    Text(
                      config.customSettings['motto'] ??
                          'Excellence in Business',
                      style: Theme.of(
                        context,
                      ).textTheme.bodyMedium?.copyWith(color: Colors.white70),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Text(
            'Drive your business forward with our comprehensive suite of tools and analytics.',
            style: Theme.of(
              context,
            ).textTheme.bodyLarge?.copyWith(color: Colors.white),
          ),
        ],
      ),
    );
  }

  /// Business metrics overview
  static Widget _buildBusinessMetricsOverview(
    BuildContext context,
    TenantConfig config,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Business Overview',
          style: Theme.of(
            context,
          ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: MediaQuery.of(context).size.width > 600 ? 4 : 2,
          mainAxisSpacing: 16,
          crossAxisSpacing: 16,
          childAspectRatio: 1.2,
          children: [
            _buildMetricCard(
              context,
              'Active Projects',
              '24',
              Icons.work,
              config,
              Colors.blue,
            ),
            _buildMetricCard(
              context,
              'Total Clients',
              '156',
              Icons.people,
              config,
              Colors.green,
            ),
            _buildMetricCard(
              context,
              'Revenue',
              '\$125K',
              Icons.attach_money,
              config,
              Colors.orange,
            ),
            _buildMetricCard(
              context,
              'Team Members',
              '42',
              Icons.group,
              config,
              Colors.purple,
            ),
          ],
        ),
      ],
    );
  }

  /// Recent projects section
  static Widget _buildRecentProjectsSection(
    BuildContext context,
    TenantConfig config,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Recent Projects',
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
            ),
            TextButton(
              onPressed: () => _showFeatureDialog(
                context,
                'All Projects',
                'View all your business projects',
              ),
              child: const Text('View All'),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Card(
          child: ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: 3,
            separatorBuilder: (context, index) => const Divider(),
            itemBuilder: (context, index) {
              final projects = [
                {
                  'name': 'Website Redesign',
                  'client': 'Tech Corp',
                  'progress': 0.75,
                  'status': 'In Progress',
                },
                {
                  'name': 'Mobile App Development',
                  'client': 'StartupXYZ',
                  'progress': 0.45,
                  'status': 'Active',
                },
                {
                  'name': 'Marketing Campaign',
                  'client': 'RetailCo',
                  'progress': 0.90,
                  'status': 'Near Completion',
                },
              ];

              final project = projects[index];
              return ListTile(
                leading: CircleAvatar(
                  backgroundColor: _parseColor(
                    config.theme.primaryColors.primary,
                  ),
                  child: Icon(Icons.work, color: Colors.white),
                ),
                title: Text(project['name'] as String),
                subtitle: Text(
                  'Client: ${project['client']} • ${project['status']}',
                ),
                trailing: SizedBox(
                  width: 60,
                  child: LinearProgressIndicator(
                    value: project['progress'] as double,
                    backgroundColor: Colors.grey[300],
                    valueColor: AlwaysStoppedAnimation<Color>(
                      _parseColor(config.theme.primaryColors.primary),
                    ),
                  ),
                ),
                onTap: () => _showFeatureDialog(
                  context,
                  project['name'] as String,
                  'View project details and manage tasks',
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  /// Business quick actions
  static Widget _buildBusinessQuickActions(
    BuildContext context,
    TenantConfig config,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Quick Actions',
          style: Theme.of(
            context,
          ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        Wrap(
          spacing: 12,
          runSpacing: 12,
          children: [
            _buildActionButton(
              context,
              'New Project',
              Icons.add_business,
              config,
              () => _showFeatureDialog(
                context,
                'New Project',
                'Create a new business project',
              ),
            ),
            _buildActionButton(
              context,
              'Add Client',
              Icons.person_add,
              config,
              () => _showFeatureDialog(
                context,
                'Add Client',
                'Add a new client to your portfolio',
              ),
            ),
            _buildActionButton(
              context,
              'Generate Report',
              Icons.assessment,
              config,
              () => _showFeatureDialog(
                context,
                'Generate Report',
                'Create business performance reports',
              ),
            ),
            _buildActionButton(
              context,
              'Schedule Meeting',
              Icons.schedule,
              config,
              () => _showFeatureDialog(
                context,
                'Schedule Meeting',
                'Schedule a client or team meeting',
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// Build metric card
  static Widget _buildMetricCard(
    BuildContext context,
    String title,
    String value,
    IconData icon,
    TenantConfig config,
    Color color,
  ) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, size: 32, color: color),
            const SizedBox(height: 8),
            Text(
              value,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            Text(
              title,
              style: Theme.of(context).textTheme.bodySmall,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// Build action button
  static Widget _buildActionButton(
    BuildContext context,
    String label,
    IconData icon,
    TenantConfig config,
    VoidCallback onPressed,
  ) {
    return ElevatedButton.icon(
      onPressed: onPressed,
      icon: Icon(icon, size: 20),
      label: Text(label),
      style: ElevatedButton.styleFrom(
        backgroundColor: _parseColor(config.theme.primaryColors.primary),
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      ),
    );
  }
}
