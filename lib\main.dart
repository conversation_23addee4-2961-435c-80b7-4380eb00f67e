import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_localizations/flutter_localizations.dart';

import 'core/injection/injection.dart';
import 'core/config/build_config.dart';
import 'features/tenant/presentation/bloc/tenant_bloc.dart';
import 'features/tenant/domain/entities/tenant_config.dart';
import 'shared/theme/tenant_theme_service.dart';
import 'shared/localization/app_localizations.dart';
import 'shared/services/tenant_asset_service.dart';
import 'shared/widgets/user_info_card.dart';
import 'shared/widgets/responsive_home_page.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize dependencies
  await configureDependencies();

  // Print build info for debugging
  print('Building for tenant: ${BuildConfig.tenantId}');
  print('App name: ${BuildConfig.appName}');
  print('Build info: ${BuildConfig.buildInfo}');

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider<TenantBloc>(
      create: (context) => getIt<TenantBloc>()
        ..add(
          LoadTenantConfig(BuildConfig.tenantId),
        ), // Load tenant based on build configuration
      child: BlocBuilder<TenantBloc, TenantState>(
        builder: (context, state) {
          ThemeData theme = ThemeData.from(
            colorScheme: ColorScheme.fromSeed(seedColor: Colors.blue),
            useMaterial3: true,
          );

          ThemeData darkTheme = ThemeData.from(
            colorScheme: ColorScheme.fromSeed(
              seedColor: Colors.blue,
              brightness: Brightness.dark,
            ),
            useMaterial3: true,
          );

          // Apply tenant-specific theme if available
          if (state is TenantLoaded) {
            theme = TenantThemeService.createThemeFromTenantConfig(
              state.config,
              isDarkMode: false,
            );
            darkTheme = TenantThemeService.createThemeFromTenantConfig(
              state.config,
              isDarkMode: true,
            );
          }

          return MaterialApp(
            title: BuildConfig.appName,
            theme: theme,
            darkTheme: darkTheme,
            themeMode: ThemeMode.system,

            // Internationalization
            localizationsDelegates: const [
              AppLocalizations.delegate,
              GlobalMaterialLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate,
            ],
            supportedLocales: const [
              Locale('en', ''), // English
              Locale('es', ''), // Spanish
              Locale('fr', ''), // French
              Locale('de', ''), // German
              Locale('zh', ''), // Chinese
              Locale('ja', ''), // Japanese
              Locale('ar', ''), // Arabic
            ],

            // Navigation
            home: const ResponsiveHomePage(),

            // Debug banner
            debugShowCheckedModeBanner: false,
          );
        },
      ),
    );
  }
}

// Sample HomePage
class HomePage extends StatelessWidget {
  const HomePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(context.t('app_title', 'Multi-Tenant App')),
        actions: [
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () {
              // TODO: Navigate to settings
            },
          ),
        ],
      ),
      body: BlocBuilder<TenantBloc, TenantState>(
        builder: (context, state) {
          if (state is TenantLoading) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const CircularProgressIndicator(),
                  const SizedBox(height: 16),
                  Text(context.t('loading', 'Loading...')),
                ],
              ),
            );
          }

          if (state is TenantError) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 64,
                    color: Theme.of(context).colorScheme.error,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    context.t('error', 'Error'),
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                  const SizedBox(height: 8),
                  Text(state.message),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      context.read<TenantBloc>().add(
                        const LoadTenantConfig('default'),
                      );
                    },
                    child: Text(context.t('retry', 'Retry')),
                  ),
                ],
              ),
            );
          }

          if (state is TenantLoaded) {
            return _buildTenantAwareLayout(context, state.config);
          }

          return const SizedBox.shrink();
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          // Show snackbar with tenant switching info
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                context.t(
                  'use_chips_above',
                  'Use the tenant chips above to switch themes!',
                ),
              ),
            ),
          );
        },
        child: const Icon(Icons.info),
      ),
    );
  }

  Widget _buildTenantAwareLayout(BuildContext context, TenantConfig config) {
    final layout = config.layout;

    // Build different layouts based on tenant configuration
    switch (layout.layoutType) {
      case 'compact':
        return _buildCompactLayout(context, config);
      case 'sidebar':
        return _buildSidebarLayout(context, config);
      case 'cards':
        return _buildCardsLayout(context, config);
      default:
        return _buildStandardLayout(context, config);
    }
  }

  Widget _buildStandardLayout(BuildContext context, TenantConfig config) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(
        TenantLayoutService.getSpacing(config.layout, 'medium'),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // User profile section
          CurrentUserInfoCard(
            tenantConfig: config,
            isCompact: false,
            onProfileTap: () => _showFeatureDialog(
              context,
              'User Profile',
              'View and edit your profile information',
            ),
            onSettingsTap: () => _showFeatureDialog(
              context,
              'Settings',
              'Manage your account settings',
            ),
          ),
          SizedBox(
            height: TenantLayoutService.getSpacing(config.layout, 'medium'),
          ),
          _buildTenantInfoCard(context, config),
          SizedBox(
            height: TenantLayoutService.getSpacing(config.layout, 'medium'),
          ),
          _buildFeaturesSection(context, config),
        ],
      ),
    );
  }

  Widget _buildCompactLayout(BuildContext context, TenantConfig config) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(
        TenantLayoutService.getSpacing(config.layout, 'small'),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // User profile section
          CurrentUserInfoCard(
            tenantConfig: config,
            isCompact: true,
            onProfileTap: () => _showFeatureDialog(
              context,
              'User Profile',
              'View and edit your profile information',
            ),
            onSettingsTap: () => _showFeatureDialog(
              context,
              'Settings',
              'Manage your account settings',
            ),
          ),
          SizedBox(
            height: TenantLayoutService.getSpacing(config.layout, 'small'),
          ),
          _buildCompactTenantInfo(context, config),
          SizedBox(
            height: TenantLayoutService.getSpacing(config.layout, 'small'),
          ),
          _buildCompactFeatures(context, config),
        ],
      ),
    );
  }

  Widget _buildSidebarLayout(BuildContext context, TenantConfig config) {
    return Row(
      children: [
        if (config.layout.showSidebar)
          Container(
            width: 280,
            padding: EdgeInsets.all(
              TenantLayoutService.getSpacing(config.layout, 'medium'),
            ),
            color: Theme.of(context).colorScheme.surfaceContainerHighest,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildTenantInfoCard(context, config),
                SizedBox(
                  height: TenantLayoutService.getSpacing(
                    config.layout,
                    'medium',
                  ),
                ),
                // Add user profile section for demonstration
                CurrentUserInfoCard(
                  tenantConfig: config,
                  isCompact: true,
                  onProfileTap: () => _showFeatureDialog(
                    context,
                    'User Profile',
                    'View and edit your profile information',
                  ),
                  onSettingsTap: () => _showFeatureDialog(
                    context,
                    'Settings',
                    'Manage your account settings',
                  ),
                ),
              ],
            ),
          ),
        Expanded(
          child: SingleChildScrollView(
            padding: EdgeInsets.all(
              TenantLayoutService.getSpacing(config.layout, 'medium'),
            ),
            child: _buildFeaturesSection(context, config),
          ),
        ),
      ],
    );
  }

  Widget _buildCardsLayout(BuildContext context, TenantConfig config) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(
        TenantLayoutService.getSpacing(config.layout, 'large'),
      ),
      child: Center(
        child: ConstrainedBox(
          constraints: const BoxConstraints(maxWidth: 800),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // User profile section
              CurrentUserInfoCard(
                tenantConfig: config,
                isCompact: false,
                onProfileTap: () => _showFeatureDialog(
                  context,
                  'User Profile',
                  'View and edit your profile information',
                ),
                onSettingsTap: () => _showFeatureDialog(
                  context,
                  'Settings',
                  'Manage your account settings',
                ),
              ),
              SizedBox(
                height: TenantLayoutService.getSpacing(config.layout, 'large'),
              ),
              _buildPremiumTenantCard(context, config),
              SizedBox(
                height: TenantLayoutService.getSpacing(config.layout, 'large'),
              ),
              _buildCardGridFeatures(context, config),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTenantInfoCard(BuildContext context, TenantConfig config) {
    return TenantAwareCard(
      tenantConfig: config,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              context.t('welcome', 'Welcome'),
              style: Theme.of(context).textTheme.headlineMedium,
            ),
            const SizedBox(height: 8),
            Text(
              'Current Tenant: ${config.displayName}',
              style: Theme.of(context).textTheme.bodyLarge,
            ),
            const SizedBox(height: 16),
            _buildTenantDetails(context, config),
          ],
        ),
      ),
    );
  }

  Widget _buildCompactTenantInfo(BuildContext context, TenantConfig config) {
    return TenantAwareCard(
      tenantConfig: config,
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Row(
          children: [
            Icon(Icons.business, color: Theme.of(context).colorScheme.primary),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    config.displayName,
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  Text(
                    'Layout: ${config.layout.layoutType}',
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPremiumTenantCard(BuildContext context, TenantConfig config) {
    return TenantAwareCard(
      tenantConfig: config,
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          children: [
            Icon(
              Icons.diamond,
              size: 48,
              color: Theme.of(context).colorScheme.primary,
            ),
            const SizedBox(height: 16),
            Text(
              config.displayName,
              style: Theme.of(
                context,
              ).textTheme.headlineMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Text(
              config.customSettings['customWelcomeMessage'] ?? 'Welcome!',
              style: Theme.of(context).textTheme.bodyLarge,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            _buildTenantDetails(context, config),
          ],
        ),
      ),
    );
  }

  Widget _buildTenantDetails(BuildContext context, TenantConfig config) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Tenant ID: ${config.id}',
          style: Theme.of(context).textTheme.bodyMedium,
        ),
        Text(
          'Layout: ${config.layout.layoutType}',
          style: Theme.of(context).textTheme.bodyMedium,
        ),
        Text(
          'Theme: ${config.theme.primaryColors.primary}',
          style: Theme.of(context).textTheme.bodyMedium,
        ),
        Text(
          'Font: ${config.theme.fontFamily ?? 'Default'}',
          style: Theme.of(context).textTheme.bodyMedium,
        ),
      ],
    );
  }

  Widget _buildFeaturesSection(BuildContext context, TenantConfig config) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Features:', style: Theme.of(context).textTheme.headlineSmall),
        const SizedBox(height: 8),
        ..._buildFeatureList(context, config),
      ],
    );
  }

  Widget _buildCompactFeatures(BuildContext context, TenantConfig config) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Features:', style: Theme.of(context).textTheme.titleMedium),
        const SizedBox(height: 8),
        ..._buildFeatureList(context, config, isCompact: true),
      ],
    );
  }

  Widget _buildCardGridFeatures(BuildContext context, TenantConfig config) {
    final features = _buildFeatureList(context, config);
    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: 2,
      mainAxisSpacing: 16,
      crossAxisSpacing: 16,
      childAspectRatio: 1.2,
      children: features.take(4).toList(),
    );
  }

  List<Widget> _buildFeatureList(
    BuildContext context,
    TenantConfig config, {
    bool isCompact = false,
  }) {
    final features = [
      {
        'icon': Icons.palette,
        'title': 'Multi-Tenant Theming',
        'subtitle': 'Dynamic themes per tenant',
        'onTap': () => _showFeatureDialog(
          context,
          'Theming',
          'Each tenant has unique colors, fonts, and styling.',
        ),
      },
      {
        'icon': Icons.image,
        'title': 'Custom Assets',
        'subtitle': 'Tenant-specific images and logos',
        'onTap': () => _showFeatureDialog(
          context,
          'Assets',
          'Each tenant can have custom logos, banners, and images.',
        ),
      },
      {
        'icon': Icons.view_quilt,
        'title': 'Layout Variations',
        'subtitle': 'Different UI layouts per tenant',
        'onTap': () => _showFeatureDialog(
          context,
          'Layouts',
          'Tenants can have different layouts: standard, compact, sidebar, or cards.',
        ),
      },
      {
        'icon': Icons.language,
        'title': 'Internationalization',
        'subtitle': 'Multi-language support',
        'onTap': () => _showFeatureDialog(
          context,
          'i18n',
          'Support for multiple languages and locales.',
        ),
      },
      {
        'icon': Icons.api,
        'title': 'REST API Client',
        'subtitle': 'HTTP client integration',
        'onTap': () => _showFeatureDialog(
          context,
          'API',
          'Built-in HTTP client for REST API communication.',
        ),
      },
      {
        'icon': Icons.devices,
        'title': 'Cross-Platform',
        'subtitle': 'iOS, Android, Web, Desktop',
        'onTap': () => _showFeatureDialog(
          context,
          'Platforms',
          'Single codebase for all platforms.',
        ),
      },
    ];

    return features.map((feature) {
      return TenantAwareCard(
        tenantConfig: config,
        onTap: feature['onTap'] as VoidCallback,
        child: ListTile(
          leading: Icon(feature['icon'] as IconData),
          title: Text(feature['title'] as String),
          subtitle: isCompact ? null : Text(feature['subtitle'] as String),
          dense: isCompact,
        ),
      );
    }).toList();
  }

  void _showFeatureDialog(
    BuildContext context,
    String title,
    String description,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(description),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}
