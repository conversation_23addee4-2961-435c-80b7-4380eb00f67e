import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

class StorageService {
  final SharedPreferences _sharedPreferences;
  static const String _tenantKey = 'current_tenant';
  static const String _userPreferencesKey = 'user_preferences';

  StorageService(this._sharedPreferences);

  // Tenant Management
  Future<void> setCurrentTenant(String tenantId) async {
    await _sharedPreferences.setString(_tenantKey, tenantId);
  }

  String? getCurrentTenant() {
    return _sharedPreferences.getString(_tenantKey);
  }

  Future<void> clearCurrentTenant() async {
    await _sharedPreferences.remove(_tenantKey);
  }

  // User Preferences
  Future<void> setUserPreference(String key, dynamic value) async {
    final preferences = getUserPreferences();
    preferences[key] = value;
    await _sharedPreferences.setString(
      _userPreferencesKey,
      jsonEncode(preferences),
    );
  }

  Map<String, dynamic> getUserPreferences() {
    final preferencesString = _sharedPreferences.getString(_userPreferencesKey);
    if (preferencesString != null) {
      return Map<String, dynamic>.from(jsonDecode(preferencesString));
    }
    return {};
  }

  T? getUserPreference<T>(String key) {
    final preferences = getUserPreferences();
    return preferences[key] as T?;
  }

  // Language Settings
  Future<void> setLanguage(String languageCode) async {
    await setUserPreference('language', languageCode);
  }

  String? getLanguage() {
    return getUserPreference<String>('language');
  }

  // Theme Settings
  Future<void> setThemeMode(String themeMode) async {
    await setUserPreference('theme_mode', themeMode);
  }

  String? getThemeMode() {
    return getUserPreference<String>('theme_mode');
  }

  // Generic Storage Methods
  Future<void> setString(String key, String value) async {
    await _sharedPreferences.setString(key, value);
  }

  String? getString(String key) {
    return _sharedPreferences.getString(key);
  }

  Future<void> setInt(String key, int value) async {
    await _sharedPreferences.setInt(key, value);
  }

  int? getInt(String key) {
    return _sharedPreferences.getInt(key);
  }

  Future<void> setBool(String key, bool value) async {
    await _sharedPreferences.setBool(key, value);
  }

  bool? getBool(String key) {
    return _sharedPreferences.getBool(key);
  }

  Future<void> remove(String key) async {
    await _sharedPreferences.remove(key);
  }

  Future<void> clear() async {
    await _sharedPreferences.clear();
  }
}
