# Multi-Tenant Folder Structure Guide

## 🏗️ Overview

This document explains the improved folder-based organization for multi-tenant applications. Each tenant now has its own dedicated folder containing configuration, custom widgets, and tenant-specific logic.

## 📁 New Folder Structure

```
lib/
├── tenants/                     # All tenant-specific code
│   ├── tenant_registry.dart     # Central tenant registry
│   ├── default/                 # Default tenant
│   │   └── default_config.dart
│   ├── company_a/               # Company A tenant
│   │   ├── company_a_config.dart
│   │   └── company_a_widgets.dart
│   ├── company_b/               # Company B tenant
│   │   ├── company_b_config.dart
│   │   └── company_b_widgets.dart
│   └── ocigm/                   # OCIGM tenant
│       ├── ocigm_config.dart
│       └── ocigm_widgets.dart
├── features/                    # Core application features
│   └── tenant/                  # Tenant management feature
│       ├── domain/
│       ├── data/
│       └── presentation/
└── shared/                      # Shared utilities and services
    ├── services/
    ├── theme/
    └── localization/
```

## 🎯 Benefits of Folder-Based Organization

### 1. **Separation of Concerns**

- Each tenant's code is isolated in its own folder
- Changes to one tenant don't affect others
- Clear boundaries between tenant-specific and shared code

### 2. **Scalability**

- Easy to add new tenants by creating new folders
- No central configuration file that grows large
- Modular architecture supports unlimited tenants

### 3. **Team Collaboration**

- Different teams can work on different tenants independently
- Reduced merge conflicts
- Clear ownership of tenant-specific code

### 4. **Maintainability**

- Easier to find and modify tenant-specific logic
- Simplified debugging and testing
- Clear dependency management

### 5. **Deployment Flexibility**

- Can build tenant-specific versions if needed
- Easy to include/exclude tenants from builds
- Better code splitting opportunities

## 🔧 How It Works

### 1. Tenant Configuration Files

Each tenant has its own configuration file:

```dart
// lib/tenants/company_a/company_a_config.dart
class CompanyATenantConfig {
  static const TenantConfig config = TenantConfig(
    id: 'company_a',
    displayName: 'Company A',
    // ... tenant-specific configuration
  );
}
```

### 2. Central Registry

The `TenantRegistry` provides centralized access to all tenant configurations:

```dart
// lib/tenants/tenant_registry.dart
class TenantRegistry {
  static const Map<String, TenantConfig> _tenants = {
    'default': DefaultTenantConfig.config,
    'company_a': CompanyATenantConfig.config,
    'company_b': CompanyBTenantConfig.config,
    'ocigm': OCIGMTenantConfig.config,
  };

  static TenantConfig? getConfig(String tenantId) {
    return _tenants[tenantId];
  }
}
```

### 3. Tenant-Specific Widgets

Each tenant can have custom UI components:

```dart
// lib/tenants/company_a/company_a_widgets.dart
class CompanyAWidgets {
  static Widget buildDashboardHeader(BuildContext context, TenantConfig config) {
    // Company A specific UI implementation
  }

  static Widget buildBusinessMetrics(BuildContext context, TenantConfig config) {
    // Company A specific metrics display
  }
}
```

### 4. Usage in Application

The application uses the registry to get tenant configurations:

```dart
// In TenantBloc
TenantConfig _getDefaultTenantConfig(String tenantId) {
  final config = TenantRegistry.getConfig(tenantId);
  return config ?? TenantRegistry.getDefaultConfig();
}
```

## 📋 Tenant-Specific Configurations

### Default Tenant

- **Purpose**: Standard fallback configuration
- **Layout**: Standard layout with elevated cards
- **Features**: Basic multi-tenant functionality

### Company A

- **Purpose**: Business-focused compact layout
- **Layout**: Compact design optimized for efficiency
- **Features**: Business metrics, client management, project tracking
- **Custom Widgets**: Business dashboard, metrics cards, compact actions

### Company B

- **Purpose**: Eco-friendly sidebar layout
- **Layout**: Desktop-oriented with sidebar navigation
- **Features**: Sustainability tracking, eco-metrics
- **Custom Widgets**: Nature-themed components

### OCIGM

- **Purpose**: Premium enterprise experience
- **Layout**: Card-based premium design
- **Features**: Innovation tracking, AI analytics, premium support
- **Custom Widgets**: Premium welcome, innovation dashboard, advanced metrics

## 🚀 Adding New Tenants

To add a new tenant:

1. **Create Tenant Folder**:

   ```
   lib/tenants/new_tenant/
   ```

2. **Create Configuration**:

   ```dart
   // lib/tenants/new_tenant/new_tenant_config.dart
   class NewTenantConfig {
     static const TenantConfig config = TenantConfig(
       // Configuration here
     );
   }
   ```

3. **Create Custom Widgets** (Optional):

   ```dart
   // lib/tenants/new_tenant/new_tenant_widgets.dart
   class NewTenantWidgets {
     // Custom widgets here
   }
   ```

4. **Register in Registry**:
   ```dart
   // lib/tenants/tenant_registry.dart
   static const Map<String, TenantConfig> _tenants = {
     // existing tenants...
     'new_tenant': NewTenantConfig.config,
   };
   ```

## 🔍 Registry Features

The `TenantRegistry` provides advanced querying capabilities:

```dart
// Get all tenant IDs
List<String> allTenants = TenantRegistry.getAllTenantIds();

// Get tenants by type
List<TenantConfig> businessTenants = TenantRegistry.getTenantsByType('business');

// Get premium tenants
List<TenantConfig> premiumTenants = TenantRegistry.getPremiumTenants();

// Get display names for UI
Map<String, String> displayNames = TenantRegistry.getTenantDisplayNames();

// Check if tenant exists
bool exists = TenantRegistry.hasTenant('company_a');
```

## 🎨 Custom Widget Integration

Tenant-specific widgets can be used in layouts:

```dart
// In layout builders
Widget _buildCompactLayout(BuildContext context, TenantConfig config) {
  if (config.id == 'company_a') {
    return Column(
      children: [
        CompanyAWidgets.buildDashboardHeader(context, config),
        CompanyAWidgets.buildBusinessMetrics(context, config),
        CompanyAWidgets.buildCompactActions(context, config),
      ],
    );
  }
  // Default compact layout
  return _buildDefaultCompactLayout(context, config);
}
```

## 📊 Asset Organization

Assets remain organized by tenant folder:

```
assets/
└── images/
    └── tenants/
        ├── default/
        ├── company_a/
        ├── company_b/
        └── ocigm/
```

## 🔒 Best Practices

1. **Keep Configurations Const**: Use `const` constructors for better performance
2. **Separate Logic**: Keep business logic separate from configuration
3. **Document Changes**: Document tenant-specific features and customizations
4. **Test Isolation**: Test each tenant's configuration independently
5. **Version Control**: Use clear commit messages when modifying tenant configurations

## 🌟 Advantages Over Single Configuration

| Aspect                 | Single Config   | Folder-Based         |
| ---------------------- | --------------- | -------------------- |
| **File Size**          | Large, unwieldy | Small, focused files |
| **Merge Conflicts**    | High risk       | Low risk             |
| **Team Collaboration** | Difficult       | Easy                 |
| **Code Reuse**         | Limited         | High                 |
| **Testing**            | Complex         | Simple               |
| **Maintenance**        | Difficult       | Easy                 |
| **Scalability**        | Poor            | Excellent            |

This folder-based approach provides a much more maintainable and scalable solution for multi-tenant applications, especially as the number of tenants and their complexity grows.
