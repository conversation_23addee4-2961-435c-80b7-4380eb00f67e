import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../domain/entities/tenant_config.dart';
import '../../../../tenants/tenant_registry.dart';

// Events
abstract class TenantEvent extends Equatable {
  const TenantEvent();

  @override
  List<Object> get props => [];
}

class LoadTenantConfig extends TenantEvent {
  final String tenantId;

  const LoadTenantConfig(this.tenantId);

  @override
  List<Object> get props => [tenantId];
}

class SwitchTenantEvent extends TenantEvent {
  final String tenantId;

  const SwitchTenantEvent(this.tenantId);

  @override
  List<Object> get props => [tenantId];
}

class TenantConfigChanged extends TenantEvent {
  final TenantConfig config;

  const TenantConfigChanged(this.config);

  @override
  List<Object> get props => [config];
}

// States
abstract class TenantState extends Equatable {
  const TenantState();

  @override
  List<Object> get props => [];
}

class TenantInitial extends TenantState {}

class TenantLoading extends TenantState {}

class TenantSwitching extends TenantState {}

class TenantLoaded extends TenantState {
  final TenantConfig config;

  const TenantLoaded(this.config);

  @override
  List<Object> get props => [config];
}

class TenantError extends TenantState {
  final String message;

  const TenantError(this.message);

  @override
  List<Object> get props => [message];
}

// BLoC
class TenantBloc extends Bloc<TenantEvent, TenantState> {
  TenantBloc() : super(TenantInitial()) {
    on<LoadTenantConfig>(_onLoadTenantConfig);
    on<SwitchTenantEvent>(_onSwitchTenant);
    on<TenantConfigChanged>(_onTenantConfigChanged);
  }

  Future<void> _onLoadTenantConfig(
    LoadTenantConfig event,
    Emitter<TenantState> emit,
  ) async {
    emit(TenantLoading());

    try {
      // For demo purposes, we'll load a default configuration
      await Future.delayed(const Duration(milliseconds: 500));

      final config = _getDefaultTenantConfig(event.tenantId);
      emit(TenantLoaded(config));
    } catch (e) {
      emit(TenantError('Failed to load tenant configuration: $e'));
    }
  }

  Future<void> _onSwitchTenant(
    SwitchTenantEvent event,
    Emitter<TenantState> emit,
  ) async {
    emit(TenantSwitching());

    try {
      await Future.delayed(const Duration(milliseconds: 300));
      add(LoadTenantConfig(event.tenantId));
    } catch (e) {
      emit(TenantError('Failed to switch tenant: $e'));
    }
  }

  void _onTenantConfigChanged(
    TenantConfigChanged event,
    Emitter<TenantState> emit,
  ) {
    emit(TenantLoaded(event.config));
  }

  TenantConfig _getDefaultTenantConfig(String tenantId) {
    // Use the tenant registry to get configuration
    final config = TenantRegistry.getConfig(tenantId);
    if (config != null) {
      return config;
    }

    // Fallback to default if tenant not found
    return TenantRegistry.getDefaultConfig();
  }
}
