import '../features/tenant/domain/entities/tenant_config.dart';
import 'default/default_config.dart';
import 'company_a/company_a_config.dart';
import 'company_b/company_b_config.dart';
import 'ocigm/ocigm_config.dart';

/// Registry for all tenant configurations
///
/// This provides a centralized way to access tenant configurations
/// while keeping each tenant's configuration in its own folder
class TenantRegistry {
  static const Map<String, TenantConfig> _tenants = {
    'default': DefaultTenantConfig.config,
    'company_a': CompanyATenantConfig.config,
    'company_b': CompanyBTenantConfig.config,
    'ocigm': OCIGMTenantConfig.config,
  };

  /// Get configuration for a specific tenant
  static TenantConfig? getConfig(String tenantId) {
    return _tenants[tenantId];
  }

  /// Get all available tenant IDs
  static List<String> getAllTenantIds() {
    return _tenants.keys.toList();
  }

  /// Get all tenant configurations
  static Map<String, TenantConfig> getAllConfigs() {
    return Map.unmodifiable(_tenants);
  }

  /// Check if a tenant exists
  static bool hasTenant(String tenantId) {
    return _tenants.containsKey(tenantId);
  }

  /// Get tenant display names for UI
  static Map<String, String> getTenantDisplayNames() {
    return _tenants.map((key, config) => MapEntry(key, config.displayName));
  }

  /// Get tenants by type/category
  static List<TenantConfig> getTenantsByType(String type) {
    return _tenants.values
        .where(
          (config) =>
              config.customSettings['companyType'] == type ||
              config.customSettings['industry'] == type,
        )
        .toList();
  }

  /// Get premium tenants
  static List<TenantConfig> getPremiumTenants() {
    return _tenants.values
        .where(
          (config) =>
              config.customSettings['tier'] == 'enterprise' ||
              config.customSettings['premiumFeatures'] == true,
        )
        .toList();
  }

  /// Get default fallback configuration
  static TenantConfig getDefaultConfig() {
    return _tenants['default']!;
  }
}
