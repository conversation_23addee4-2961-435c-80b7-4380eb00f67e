# Multi-Tenant Flutter App - Build & Run Guide

This guide explains how to build and run your multi-tenant Flutter application for different tenants using command line tools.

## Available Tenants

- `default` - Default tenant configuration
- `company_a` - Company A with custom theme and layout
- `company_b` - Company B with different branding
- `ocigm` - OCIGM with specialized layout and assets

## Quick Commands

### Development (Run with Hot Reload)

**Windows:**

```batch
# Run default tenant on Chrome
scripts\run-tenant.bat

# Run specific tenant
scripts\run-tenant.bat ocigm chrome
scripts\run-tenant.bat company_a chrome
scripts\run-tenant.bat company_b chrome

# Run on different devices
scripts\run-tenant.bat ocigm windows
scripts\run-tenant.bat company_a edge
```

**Linux/macOS:**

```bash
# Run default tenant on Chrome
./scripts/run-tenant.sh

# Run specific tenant
./scripts/run-tenant.sh ocigm chrome
./scripts/run-tenant.sh company_a chrome
./scripts/run-tenant.sh company_b chrome

# Run on different devices
./scripts/run-tenant.sh ocigm macos
./scripts/run-tenant.sh company_a ios
```

### Production Builds

**Windows:**

```batch
# Build web version for OCIGM tenant
scripts\build-tenant.bat ocigm web release

# Build Android APK for Company A
scripts\build-tenant.bat company_a apk release

# Build Windows app for Company B
scripts\build-tenant.bat company_b windows release

# Build all platforms for default tenant
scripts\build-tenant.bat default web release
scripts\build-tenant.bat default apk release
scripts\build-tenant.bat default windows release
```

**Linux/macOS:**

```bash
# Build web version for OCIGM tenant
./scripts/build-tenant.sh ocigm web release

# Build Android APK for Company A
./scripts/build-tenant.sh company_a apk release

# Build iOS app for Company B (macOS only)
./scripts/build-tenant.sh company_b ios release

# Build macOS app for default tenant (macOS only)
./scripts/build-tenant.sh default macos release
```

## Manual Flutter Commands

If you prefer to run Flutter commands directly:

### Development

```bash
# Run for web with OCIGM tenant
flutter run -d chrome --dart-define=TENANT_ID=ocigm

# Run for Windows with Company A tenant
flutter run -d windows --dart-define=TENANT_ID=company_a

# Run for mobile device with Company B tenant
flutter run -d device --dart-define=TENANT_ID=company_b
```

### Production Builds

```bash
# Web build for OCIGM
flutter build web --dart-define=TENANT_ID=ocigm --release

# Android APK for Company A
flutter build apk --dart-define=TENANT_ID=company_a --release

# Windows executable for Company B
flutter build windows --dart-define=TENANT_ID=company_b --release

# iOS build for default tenant (macOS only)
flutter build ios --dart-define=TENANT_ID=default --release
```

## Android Simulator Setup and Usage

### Prerequisites

Before running on Android simulator, ensure you have:

1. **Android Studio** installed with Android SDK
2. **Android Virtual Device (AVD)** created
3. **Flutter Android toolchain** properly configured

### Setting Up Android Simulator

#### 1. Install Android Studio

```bash
# Download from https://developer.android.com/studio
# Install Android Studio with default settings
```

#### 2. Create Android Virtual Device (AVD)

```bash
# Option 1: Using Android Studio GUI
# 1. Open Android Studio
# 2. Go to Tools → AVD Manager
# 3. Click "Create Virtual Device"
# 4. Choose device (e.g., Pixel 7)
# 5. Download and select system image (e.g., API 34)
# 6. Configure AVD settings
# 7. Click "Finish"

# Option 2: Using Command Line
flutter emulators --launch <emulator_name>
```

#### 3. Verify Flutter Android Setup

```bash
# Check if Android toolchain is properly configured
flutter doctor

# Should show:
# ✓ Android toolchain - develop for Android devices
# ✓ Android Studio
```

### Running Multi-Tenant App on Android Simulator

#### Quick Start

**Windows:**

```batch
# Start Android emulator and run default tenant
scripts\run-tenant.bat default android

# Run specific tenant on Android
scripts\run-tenant.bat company_a android
scripts\run-tenant.bat company_b android
scripts\run-tenant.bat ocigm android
```

**Linux/macOS:**

```bash
# Start Android emulator and run default tenant
./scripts/run-tenant.sh default android

# Run specific tenant on Android
./scripts/run-tenant.sh company_a android
./scripts/run-tenant.sh company_b android
./scripts/run-tenant.sh ocigm android
```

#### Manual Commands

```bash
# 1. List available emulators
flutter emulators

# 2. Start specific emulator
flutter emulators --launch <emulator_name>

# 3. Run app with tenant configuration
flutter run -d android --dart-define=TENANT_ID=company_a

# 4. Or specify exact emulator device ID
flutter devices
flutter run -d <device_id> --dart-define=TENANT_ID=ocigm
```

### Android-Specific Features

#### Mobile Navigation Testing

When running on Android simulator, you'll see:

- **Bottom Navigation Bar**: Optimized for mobile touch interaction
- **Responsive Layout**: Single-column layout for mobile screens
- **Touch-Friendly UI**: Larger touch targets and mobile-optimized spacing

#### Testing Different Screen Sizes

```bash
# Test on different Android device sizes
flutter emulators --launch pixel_7        # Large phone
flutter emulators --launch pixel_tablet   # Tablet (if available)
```

### Troubleshooting Android Simulator

#### Common Issues

1. **"No devices found"**:

   ```bash
   # Check if emulator is running
   flutter devices

   # Start emulator manually
   flutter emulators --launch <emulator_name>
   ```

2. **"Android toolchain not found"**:

   ```bash
   # Run Flutter doctor
   flutter doctor

   # Accept Android licenses
   flutter doctor --android-licenses
   ```

3. **"Emulator not starting"**:

   ```bash
   # Check available emulators
   flutter emulators

   # If none available, create one in Android Studio
   # Tools → AVD Manager → Create Virtual Device
   ```

4. **"Build fails on Android"**:

   ```bash
   # Clean and rebuild
   flutter clean
   flutter pub get
   flutter run -d android --dart-define=TENANT_ID=company_a
   ```

### Performance Tips

#### Optimal Emulator Settings

- **RAM**: 4GB or more for smooth performance
- **Storage**: 2GB+ available space
- **Graphics**: Hardware acceleration enabled
- **CPU**: x86_64 image for better performance

#### Hot Reload on Android

```bash
# After making changes, use hot reload
r  # Hot reload
R  # Hot restart
```

### Build Output Locations

### Web Builds

- Output: `build/web/`
- Deploy the contents of this folder to your web server

### Android Builds

- APK: `build/app/outputs/flutter-apk/app-release.apk`
- App Bundle: `build/app/outputs/bundle/release/app-release.aab`

### Windows Builds

- Executable: `build/windows/x64/runner/Release/`
- The entire folder contains the distributable application

### iOS Builds (macOS only)

- Output: `build/ios/Release-iphoneos/`
- Use Xcode for final signing and distribution

### macOS Builds (macOS only)

- Output: `build/macos/Build/Products/Release/`
- The .app bundle can be distributed directly

## Environment Variables

The tenant selection is controlled by the `TENANT_ID` environment variable:

```bash
# Set environment variable directly (alternative approach)
export TENANT_ID=ocigm
flutter run -d chrome

# Windows equivalent
set TENANT_ID=ocigm
flutter run -d chrome
```

## Debugging

To see which tenant is being loaded:

1. Check the console output when starting the app
2. Look for log messages like "Building app for tenant: ocigm"
3. Verify the app title matches the expected tenant

## Adding New Tenants

1. Create a new configuration file in `lib/tenants/configs/`
2. Add tenant ID to the scripts' `AVAILABLE_TENANTS` list
3. Update this README with the new tenant information

## Troubleshooting

### Common Issues

1. **"Invalid tenant ID" error**: Check that the tenant ID matches exactly one of the available tenants
2. **Build fails**: Ensure all dependencies are installed with `flutter pub get`
3. **Wrong tenant loading**: Verify the `TENANT_ID` environment variable is set correctly

### Getting Help

Run the build scripts without parameters to see usage information:

```bash
# Windows
scripts\build-tenant.bat

# Linux/macOS
./scripts/build-tenant.sh
```
