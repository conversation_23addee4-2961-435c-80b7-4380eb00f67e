import 'package:flutter/material.dart';
import '../../features/tenant/domain/entities/tenant_config.dart';
import '../../shared/widgets/user_info_card.dart';

/// Custom widgets specific to Company B tenant (EcoTech Solutions)
class CompanyBWidgets {
  /// Company B specific home page content
  static Widget buildHomePage(BuildContext context, TenantConfig config) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // User info section
          CurrentUserInfoCard(
            tenantConfig: config,
            isCompact: false,
            onProfileTap: () => _showFeatureDialog(
              context,
              'Eco Profile',
              'View and edit your environmental profile',
            ),
            onSettingsTap: () => _showFeatureDialog(
              context,
              'Sustainability Settings',
              'Manage your eco-friendly preferences',
            ),
          ),
          const SizedBox(height: 24),

          // Sustainability Welcome Section
          _buildSustainabilityWelcomeSection(context, config),
          const SizedBox(height: 24),

          // Environmental Impact Dashboard
          _buildEnvironmentalDashboard(context, config),
          const SizedBox(height: 24),

          // Green Projects Section
          _buildGreenProjectsSection(context, config),
          const SizedBox(height: 24),

          // Eco Actions
          _buildEcoActions(context, config),
        ],
      ),
    );
  }

  /// Sustainability-specific welcome section
  static Widget _buildSustainabilityWelcomeSection(
    BuildContext context,
    TenantConfig config,
  ) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            _parseColor(config.theme.primaryColors.primary),
            Colors.green.shade300,
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.eco, color: Colors.white, size: 32),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Welcome to ${config.displayName}',
                      style: Theme.of(context).textTheme.headlineSmall
                          ?.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                    ),
                    Text(
                      'Sustainable Technology for a Better Tomorrow',
                      style: Theme.of(
                        context,
                      ).textTheme.bodyMedium?.copyWith(color: Colors.white70),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Text(
            'Join us in creating innovative solutions that protect our planet while driving technological advancement.',
            style: Theme.of(
              context,
            ).textTheme.bodyLarge?.copyWith(color: Colors.white),
          ),
        ],
      ),
    );
  }

  /// Environmental impact dashboard
  static Widget _buildEnvironmentalDashboard(
    BuildContext context,
    TenantConfig config,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Environmental Impact',
          style: Theme.of(
            context,
          ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: MediaQuery.of(context).size.width > 600 ? 4 : 2,
          mainAxisSpacing: 16,
          crossAxisSpacing: 16,
          childAspectRatio: 1.2,
          children: [
            _buildEcoMetricCard(
              context,
              'CO₂ Reduced',
              '2.4T',
              Icons.cloud_off,
              config,
              Colors.green,
            ),
            _buildEcoMetricCard(
              context,
              'Trees Planted',
              '1,250',
              Icons.park,
              config,
              Colors.green.shade700,
            ),
            _buildEcoMetricCard(
              context,
              'Energy Saved',
              '450kWh',
              Icons.bolt,
              config,
              Colors.amber,
            ),
            _buildEcoMetricCard(
              context,
              'Water Conserved',
              '890L',
              Icons.water_drop,
              config,
              Colors.blue,
            ),
          ],
        ),
      ],
    );
  }

  /// Green projects section
  static Widget _buildGreenProjectsSection(
    BuildContext context,
    TenantConfig config,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Green Projects',
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
            ),
            TextButton(
              onPressed: () => _showFeatureDialog(
                context,
                'All Green Projects',
                'View all sustainability initiatives',
              ),
              child: const Text('View All'),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Card(
          child: ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: 3,
            separatorBuilder: (context, index) => const Divider(),
            itemBuilder: (context, index) {
              final projects = [
                {
                  'name': 'Solar Panel Installation',
                  'location': 'Corporate Campus',
                  'progress': 0.80,
                  'impact': '15% Energy Reduction',
                },
                {
                  'name': 'Urban Farming Initiative',
                  'location': 'City Center',
                  'progress': 0.60,
                  'impact': '200kg Fresh Produce',
                },
                {
                  'name': 'Electric Vehicle Fleet',
                  'location': 'Fleet Operations',
                  'progress': 0.40,
                  'impact': '50% Emission Cut',
                },
              ];

              final project = projects[index];
              return ListTile(
                leading: CircleAvatar(
                  backgroundColor: Colors.green,
                  child: Icon(Icons.eco, color: Colors.white),
                ),
                title: Text(project['name'] as String),
                subtitle: Text('${project['location']} • ${project['impact']}'),
                trailing: SizedBox(
                  width: 60,
                  child: LinearProgressIndicator(
                    value: project['progress'] as double,
                    backgroundColor: Colors.grey[300],
                    valueColor: const AlwaysStoppedAnimation<Color>(
                      Colors.green,
                    ),
                  ),
                ),
                onTap: () => _showFeatureDialog(
                  context,
                  project['name'] as String,
                  'View project details and environmental impact',
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  /// Eco-friendly quick actions
  static Widget _buildEcoActions(BuildContext context, TenantConfig config) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Eco Actions',
          style: Theme.of(
            context,
          ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        Wrap(
          spacing: 12,
          runSpacing: 12,
          children: [
            _buildEcoActionButton(
              context,
              'Plant Trees',
              Icons.park,
              config,
              () => _showFeatureDialog(
                context,
                'Tree Planting',
                'Contribute to reforestation efforts',
              ),
            ),
            _buildEcoActionButton(
              context,
              'Carbon Offset',
              Icons.cloud_off,
              config,
              () => _showFeatureDialog(
                context,
                'Carbon Offset',
                'Calculate and offset your carbon footprint',
              ),
            ),
            _buildEcoActionButton(
              context,
              'Green Energy',
              Icons.solar_power,
              config,
              () => _showFeatureDialog(
                context,
                'Green Energy',
                'Switch to renewable energy sources',
              ),
            ),
            _buildEcoActionButton(
              context,
              'Recycle',
              Icons.recycling,
              config,
              () => _showFeatureDialog(
                context,
                'Recycling Program',
                'Join our recycling initiatives',
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// Build eco metric card
  static Widget _buildEcoMetricCard(
    BuildContext context,
    String title,
    String value,
    IconData icon,
    TenantConfig config,
    Color color,
  ) {
    return Card(
      elevation: 2,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          gradient: LinearGradient(
            colors: [color.withOpacity(0.1), color.withOpacity(0.05)],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, size: 32, color: color),
            const SizedBox(height: 8),
            Text(
              value,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            Text(
              title,
              style: Theme.of(context).textTheme.bodySmall,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// Build eco action button
  static Widget _buildEcoActionButton(
    BuildContext context,
    String label,
    IconData icon,
    TenantConfig config,
    VoidCallback onPressed,
  ) {
    return ElevatedButton.icon(
      onPressed: onPressed,
      icon: Icon(icon, size: 20),
      label: Text(label),
      style: ElevatedButton.styleFrom(
        backgroundColor: Colors.green,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      ),
    );
  }

  /// Show feature dialog
  static void _showFeatureDialog(
    BuildContext context,
    String title,
    String description,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(description),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  /// Parse color from hex string
  static Color _parseColor(String hexString) {
    try {
      return Color(int.parse(hexString.replaceFirst('#', '0xFF')));
    } catch (e) {
      return Colors.blue; // Fallback color
    }
  }
}
