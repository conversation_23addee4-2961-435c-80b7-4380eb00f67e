# Multi-Tenant Layout and Asset Management Guide

This guide explains how to handle different images, layouts, and UI variations for different tenants in your Flutter multi-tenant application.

## 🎨 Overview

Your multi-tenant system now supports:

- **Dynamic Layouts**: Different UI layouts per tenant (standard, compact, sidebar, cards)
- **Custom Assets**: Tenant-specific images, logos, and resources
- **Layout Configuration**: Configurable spacing, navigation styles, and component settings
- **Theme Integration**: Seamless integration with existing theme system

## 📁 Asset Organization Structure

```
assets/
└── images/
    └── tenants/
        ├── default/           # Default tenant assets
        │   ├── logo.png
        │   ├── logo_light.png
        │   ├── logo_dark.png
        │   ├── hero_banner.jpg
        │   └── feature.jpg
        ├── company_a/         # Company A assets
        │   ├── company_a_logo.png
        │   ├── hero_banner.jpg
        │   └── feature.jpg
        ├── company_b/         # Company B assets
        │   ├── company_b_logo.png
        │   ├── nature_banner.jpg
        │   └── eco_feature.jpg
        └── ocigm/            # OCIGM tenant assets
            ├── ocigm_logo.png
            ├── premium_banner.jpg
            ├── innovation_feature.jpg
            └── purple_pattern.png
```

## 🏗️ Layout Types

### 1. Standard Layout (`default`)

- **Style**: Traditional app layout with elevated cards
- **Navigation**: Bottom navigation
- **Spacing**: Standard spacing (8/16/24)
- **Use Case**: General purpose applications

### 2. Compact Layout (`company_a`)

- **Style**: Space-efficient with outlined cards
- **Navigation**: Bottom navigation
- **Spacing**: Tight spacing (8/12/16)
- **Use Case**: Information-dense applications, mobile-first

### 3. Sidebar Layout (`company_b`)

- **Style**: Desktop-oriented with navigation sidebar
- **Navigation**: Rail navigation with sidebar
- **Spacing**: Generous spacing (12/20/28)
- **Use Case**: Desktop applications, admin dashboards

### 4. Cards Layout (`ocigm`)

- **Style**: Premium card-based design with grid layout
- **Navigation**: Bottom navigation with elevated cards
- **Spacing**: Premium spacing (16/24/32)
- **Use Case**: Showcase applications, premium experiences

## 🛠️ Implementation

### 1. Tenant Configuration

Each tenant is configured with layout and asset information:

```dart
TenantConfig(
  id: 'ocigm',
  name: 'ocigm',
  displayName: 'OCIGM',

  // Asset configuration
  assets: TenantAssets(
    logo: 'assets/images/tenants/ocigm/ocigm_logo.png',
    logoLight: 'assets/images/tenants/ocigm/ocigm_logo_light.png',
    logoDark: 'assets/images/tenants/ocigm/ocigm_logo_dark.png',
    customImages: {
      'hero_banner': 'assets/images/tenants/ocigm/premium_banner.jpg',
      'feature_image': 'assets/images/tenants/ocigm/innovation_feature.jpg',
      'background_pattern': 'assets/images/tenants/ocigm/purple_pattern.png',
    },
  ),

  // Layout configuration
  layout: TenantLayout(
    layoutType: 'cards',           // Layout style
    showHeader: true,              // Show/hide header
    showSidebar: false,            // Show/hide sidebar
    showFooter: true,              // Show/hide footer
    appBarStyle: 'prominent',      // AppBar style
    navigationStyle: 'bottom',     // Navigation type
    cardStyle: 'elevated',         // Card style
    componentSettings: {           // Custom component settings
      'cardElevation': 8,
      'borderRadius': 16,
      'useShadows': true,
    },
    spacing: {                     // Custom spacing
      'small': 16,
      'medium': 24,
      'large': 32,
    },
  ),
)
```

### 2. Asset Service Usage

Use `TenantAssetService` to load tenant-specific assets:

```dart
// Get tenant logo (automatic light/dark switching)
String logoPath = TenantAssetService.getTenantLogo(config, isDark: false);

// Get custom image with fallback
String bannerPath = TenantAssetService.getTenantImage(
  config,
  'hero_banner',
  fallback: 'assets/images/default/banner.jpg'
);

// Check if tenant has specific image
bool hasBanner = TenantAssetService.hasTenantImage(config, 'hero_banner');
```

### 3. Layout Service Usage

Use `TenantLayoutService` to adapt UI based on tenant layout:

```dart
// Get spacing values
double spacing = TenantLayoutService.getSpacing(layout, 'medium');

// Get card configuration
Map<String, dynamic> cardConfig = TenantLayoutService.getCardConfig(layout);

// Check if feature is hidden
bool isHidden = TenantLayoutService.isFeatureHidden(layout, 'advanced_search');

// Get navigation type
NavigationType navType = TenantLayoutService.getNavigationType(layout);
```

### 4. Tenant-Aware Widgets

Use provided widgets for automatic tenant adaptation:

```dart
// Tenant-aware container with spacing and background
TenantAwareContainer(
  tenantConfig: config,
  customImageKey: 'background_pattern',
  useBackground: true,
  child: YourContent(),
)

// Tenant-aware card with proper styling
TenantAwareCard(
  tenantConfig: config,
  onTap: () => handleTap(),
  child: ListTile(
    title: Text('Feature'),
    subtitle: Text('Description'),
  ),
)
```

## 🎯 Key Features

### Dynamic Layout Switching

The app automatically adapts its layout based on the current tenant:

1. **Standard Layout**: Traditional vertical layout with standard cards
2. **Compact Layout**: Tight spacing, horizontal elements, outlined cards
3. **Sidebar Layout**: Side navigation with main content area
4. **Cards Layout**: Grid-based card layout with premium styling

### Asset Management

- **Automatic Fallbacks**: If tenant asset is missing, falls back to default
- **Theme-Aware**: Supports light/dark logo variants
- **Custom Images**: Flexible key-value system for custom tenant images
- **Organized Structure**: Clear folder structure for easy management

### Responsive Design

- **Layout Adaptation**: Different layouts work well on different screen sizes
- **Spacing System**: Consistent spacing system across all layouts
- **Component Variants**: Different card styles and elevation for different experiences

## 🔧 Configuration Options

### Layout Types

- `standard`: Traditional app layout
- `compact`: Space-efficient layout
- `sidebar`: Desktop-style sidebar layout
- `cards`: Premium card-based layout

### Navigation Styles

- `bottom`: Bottom navigation bar
- `rail`: Navigation rail (sidebar)
- `drawer`: Navigation drawer

### Card Styles

- `elevated`: Material 3 elevated cards
- `outlined`: Outlined cards
- `filled`: Filled tonal cards

### App Bar Styles

- `standard`: Standard app bar
- `compact`: Compact app bar
- `prominent`: Large prominent app bar

## 🚀 Testing Different Layouts

1. **OCIGM Tenant**: Premium cards layout with purple/orange theme

   - Layout: Cards grid with premium styling
   - Assets: Custom logos and premium banner images
   - Features: Diamond icon, welcome message, elevated cards

2. **Company A**: Compact business layout with pink theme

   - Layout: Compact horizontal elements
   - Assets: Business-themed images
   - Features: Outlined cards, tight spacing

3. **Company B**: Sidebar layout with green eco theme

   - Layout: Desktop sidebar with rail navigation
   - Assets: Nature/eco themed images
   - Features: Filled cards, generous spacing

4. **Default**: Standard layout with blue theme
   - Layout: Traditional vertical layout
   - Assets: Generic neutral images
   - Features: Standard elevated cards

## 📱 Cross-Platform Considerations

- **Web**: Sidebar layout works well on larger screens
- **Mobile**: Compact and standard layouts optimize for small screens
- **Desktop**: All layouts support desktop interaction patterns
- **Tablet**: Cards layout provides good tablet experience

## 🔄 Dynamic Switching

Users can switch between tenants in real-time to see:

- Immediate theme changes
- Layout restructuring
- Asset swapping
- Spacing adjustments
- Component style changes

This provides a complete demonstration of how a single codebase can provide vastly different user experiences based on tenant configuration.

## 🎨 Best Practices

1. **Asset Naming**: Use consistent naming conventions for logos and images
2. **Fallback Images**: Always provide fallback assets for missing tenant assets
3. **Performance**: Optimize images for web delivery
4. **Accessibility**: Ensure all layouts meet accessibility guidelines
5. **Testing**: Test each layout on different screen sizes and devices

The system is fully extensible - you can add new layout types, asset categories, and configuration options as needed for your specific multi-tenant requirements.
