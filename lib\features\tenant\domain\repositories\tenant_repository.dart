import 'package:dartz/dartz.dart';
import '../entities/tenant_config.dart';
import '../../../../core/error/failures.dart';

abstract class TenantRepository {
  Future<Either<Failure, TenantConfig>> getTenantConfig(String tenantId);
  Future<Either<Failure, List<TenantConfig>>> getAvailableTenants();
  Future<Either<Failure, void>> switchTenant(String tenantId);
  Future<Either<Failure, TenantConfig?>> getCurrentTenant();
  Future<Either<Failure, void>> cacheTenantConfig(TenantConfig config);
}
