@echo off
:: Multi-Tenant Flutter Build Script for Windows
:: Usage: build-tenant.bat [tenant-id] [platform] [build-type]
:: Example: build-tenant.bat ocigm web release

setlocal enabledelayedexpansion

:: Default values
set "TENANT_ID=default"
set "PLATFORM=web"
set "BUILD_TYPE=release"

:: Parse command line arguments
if not "%~1"=="" set "TENANT_ID=%~1"
if not "%~2"=="" set "PLATFORM=%~2"
if not "%~3"=="" set "BUILD_TYPE=%~3"

:: Available tenants
set "AVAILABLE_TENANTS=default company_a company_b ocigm"
set "AVAILABLE_PLATFORMS=web apk appbundle windows macos"

:: Validate tenant ID
echo %AVAILABLE_TENANTS% | findstr /C:"%TENANT_ID%" >nul
if errorlevel 1 (
    echo Error: Invalid tenant ID '%TENANT_ID%'
    echo Available tenants: %AVAILABLE_TENANTS%
    exit /b 1
)

:: Validate platform
echo %AVAILABLE_PLATFORMS% | findstr /C:"%PLATFORM%" >nul
if errorlevel 1 (
    echo Error: Invalid platform '%PLATFORM%'
    echo Available platforms: %AVAILABLE_PLATFORMS%
    exit /b 1
)

echo ============================================
echo Building Multi-Tenant Flutter App
echo ============================================
echo Tenant: %TENANT_ID%
echo Platform: %PLATFORM%
echo Build Type: %BUILD_TYPE%
echo ============================================

:: Set build command based on platform
if "%PLATFORM%"=="web" (
    set "BUILD_CMD=flutter build web --dart-define=TENANT_ID=%TENANT_ID% --%BUILD_TYPE%"
) else if "%PLATFORM%"=="apk" (
    set "BUILD_CMD=flutter build apk --dart-define=TENANT_ID=%TENANT_ID% --%BUILD_TYPE%"
) else if "%PLATFORM%"=="appbundle" (
    set "BUILD_CMD=flutter build appbundle --dart-define=TENANT_ID=%TENANT_ID% --%BUILD_TYPE%"
) else if "%PLATFORM%"=="windows" (
    set "BUILD_CMD=flutter build windows --dart-define=TENANT_ID=%TENANT_ID% --%BUILD_TYPE%"
) else if "%PLATFORM%"=="macos" (
    set "BUILD_CMD=flutter build macos --dart-define=TENANT_ID=%TENANT_ID% --%BUILD_TYPE%"
) else (
    echo Error: Unsupported platform '%PLATFORM%'
    exit /b 1
)

echo Executing: !BUILD_CMD!
echo.

:: Execute the build command
!BUILD_CMD!

if %errorlevel% equ 0 (
    echo.
    echo ============================================
    echo Build completed successfully!
    echo ============================================
    echo Tenant: %TENANT_ID%
    echo Platform: %PLATFORM%
    echo Build Type: %BUILD_TYPE%
    
    if "%PLATFORM%"=="web" (
        echo.
        echo Web build output: build\web\
        echo To serve locally: flutter run -d chrome --dart-define=TENANT_ID=%TENANT_ID%
    ) else if "%PLATFORM%"=="windows" (
        echo.
        echo Windows build output: build\windows\x64\runner\%BUILD_TYPE%\
    )
    echo ============================================
) else (
    echo.
    echo ============================================
    echo Build failed with error code %errorlevel%
    echo ============================================
    exit /b %errorlevel%
)

endlocal
