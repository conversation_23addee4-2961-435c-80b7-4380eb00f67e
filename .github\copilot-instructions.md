# Copilot Instructions for Multi-Tenant Flutter App

<!-- Use this file to provide workspace-specific custom instructions to <PERSON><PERSON><PERSON>. For more details, visit https://code.visualstudio.com/docs/copilot/copilot-customization#_use-a-githubcopilotinstructionsmd-file -->

## Project Overview

This is a cross-platform Flutter application with multi-tenant support targeting iOS, Android, Windows, macOS, and Web platforms.

## Architecture Guidelines

- Follow Clean Architecture principles with clear separation between presentation, domain, and data layers
- Use dependency injection with get_it package
- Implement proper error handling and logging
- Use bloc pattern for state management
- Follow SOLID principles

## Multi-Tenant Requirements

- Each tenant has unique branding (colors, logos, layouts)
- Tenant configuration should be loaded at app startup
- Support runtime theme switching
- Isolate tenant-specific assets and configurations

## Code Style Guidelines

- Use meaningful variable and function names
- Add comprehensive documentation for public APIs
- Follow Flutter/Dart style guide
- Use const constructors where possible
- Implement proper null safety

## Key Features to Maintain

- HTTP REST API client with proper error handling
- Multi-language support (i18n)
- Responsive design for different screen sizes
- Offline capability where appropriate
- Secure storage for sensitive data
