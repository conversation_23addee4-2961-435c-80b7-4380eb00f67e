// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'tenant_config.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TenantConfig _$TenantConfigFromJson(Map<String, dynamic> json) => TenantConfig(
      id: json['id'] as String,
      name: json['name'] as String,
      displayName: json['displayName'] as String,
      theme: TenantTheme.fromJson(json['theme'] as Map<String, dynamic>),
      assets: TenantAssets.fromJson(json['assets'] as Map<String, dynamic>),
      layout: TenantLayout.fromJson(json['layout'] as Map<String, dynamic>),
      customSettings:
          json['customSettings'] as Map<String, dynamic>? ?? const {},
      isActive: json['isActive'] as bool? ?? true,
      lastUpdated: json['lastUpdated'] == null
          ? null
          : DateTime.parse(json['lastUpdated'] as String),
    );

Map<String, dynamic> _$TenantConfigToJson(TenantConfig instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'displayName': instance.displayName,
      'theme': instance.theme,
      'assets': instance.assets,
      'layout': instance.layout,
      'customSettings': instance.customSettings,
      'isActive': instance.isActive,
      'lastUpdated': instance.lastUpdated?.toIso8601String(),
    };

TenantTheme _$TenantThemeFromJson(Map<String, dynamic> json) => TenantTheme(
      primaryColors: TenantColorScheme.fromJson(
          json['primaryColors'] as Map<String, dynamic>),
      darkColors: json['darkColors'] == null
          ? null
          : TenantColorScheme.fromJson(
              json['darkColors'] as Map<String, dynamic>),
      fontFamily: json['fontFamily'] as String?,
      customStyles: json['customStyles'] as Map<String, dynamic>? ?? const {},
    );

Map<String, dynamic> _$TenantThemeToJson(TenantTheme instance) =>
    <String, dynamic>{
      'primaryColors': instance.primaryColors,
      'darkColors': instance.darkColors,
      'fontFamily': instance.fontFamily,
      'customStyles': instance.customStyles,
    };

TenantColorScheme _$TenantColorSchemeFromJson(Map<String, dynamic> json) =>
    TenantColorScheme(
      primary: json['primary'] as String,
      secondary: json['secondary'] as String?,
      surface: json['surface'] as String?,
      background: json['background'] as String?,
      error: json['error'] as String?,
      onPrimary: json['onPrimary'] as String?,
      onSecondary: json['onSecondary'] as String?,
      onSurface: json['onSurface'] as String?,
      onBackground: json['onBackground'] as String?,
      onError: json['onError'] as String?,
    );

Map<String, dynamic> _$TenantColorSchemeToJson(TenantColorScheme instance) =>
    <String, dynamic>{
      'primary': instance.primary,
      'secondary': instance.secondary,
      'surface': instance.surface,
      'background': instance.background,
      'error': instance.error,
      'onPrimary': instance.onPrimary,
      'onSecondary': instance.onSecondary,
      'onSurface': instance.onSurface,
      'onBackground': instance.onBackground,
      'onError': instance.onError,
    };

TenantAssets _$TenantAssetsFromJson(Map<String, dynamic> json) => TenantAssets(
      logo: json['logo'] as String?,
      logoLight: json['logoLight'] as String?,
      logoDark: json['logoDark'] as String?,
      favicon: json['favicon'] as String?,
      splashImage: json['splashImage'] as String?,
      customImages: (json['customImages'] as Map<String, dynamic>?)?.map(
            (k, e) => MapEntry(k, e as String),
          ) ??
          const {},
    );

Map<String, dynamic> _$TenantAssetsToJson(TenantAssets instance) =>
    <String, dynamic>{
      'logo': instance.logo,
      'logoLight': instance.logoLight,
      'logoDark': instance.logoDark,
      'favicon': instance.favicon,
      'splashImage': instance.splashImage,
      'customImages': instance.customImages,
    };

TenantLayout _$TenantLayoutFromJson(Map<String, dynamic> json) => TenantLayout(
      layoutType: json['layoutType'] as String? ?? 'standard',
      showHeader: json['showHeader'] as bool? ?? true,
      showSidebar: json['showSidebar'] as bool? ?? false,
      showFooter: json['showFooter'] as bool? ?? true,
      appBarStyle: json['appBarStyle'] as String? ?? 'standard',
      navigationStyle: json['navigationStyle'] as String? ?? 'bottom',
      cardStyle: json['cardStyle'] as String? ?? 'elevated',
      componentSettings:
          json['componentSettings'] as Map<String, dynamic>? ?? const {},
      hiddenFeatures: (json['hiddenFeatures'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      spacing: (json['spacing'] as Map<String, dynamic>?)?.map(
            (k, e) => MapEntry(k, (e as num).toInt()),
          ) ??
          const {},
    );

Map<String, dynamic> _$TenantLayoutToJson(TenantLayout instance) =>
    <String, dynamic>{
      'layoutType': instance.layoutType,
      'showHeader': instance.showHeader,
      'showSidebar': instance.showSidebar,
      'showFooter': instance.showFooter,
      'appBarStyle': instance.appBarStyle,
      'navigationStyle': instance.navigationStyle,
      'cardStyle': instance.cardStyle,
      'componentSettings': instance.componentSettings,
      'hiddenFeatures': instance.hiddenFeatures,
      'spacing': instance.spacing,
    };
