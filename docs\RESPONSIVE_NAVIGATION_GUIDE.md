# Responsive Navigation System Guide

## Overview

The responsive navigation system automatically adapts the navigation layout based on screen size and platform, providing optimal user experience across mobile, tablet, and desktop devices.

## Navigation Styles

### 📱 Mobile Layout (< 600px width)

- **Navigation**: Bottom navigation bar
- **Maximum Items**: 5 navigation items (Material Design guideline)
- **Features**:
  - Large touch targets for fingers
  - Always visible navigation
  - Single-column content layout

### 📟 Tablet Layout (600px - 1200px width)

- **Navigation**: Navigation rail (sidebar)
- **Features**:
  - Space-efficient rail navigation
  - Icons with labels on selection
  - Two-column content when appropriate

### 🖥️ Desktop Layout (> 1200px width or Web)

- **Navigation**: Full sidebar navigation
- **Features**:
  - Full navigation with tenant branding
  - Always visible with tenant logo
  - Multi-column content layouts
  - Maximum content width constraints

## Tenant-Specific Navigation Items

### Default Tenant

```dart
- Home (home icon)
- Dashboard (dashboard icon)
- Settings (settings icon)
```

### Company A (Business Focus)

```dart
- Home (home icon)
- Dashboard (dashboard icon)
- Projects (business_center icon)
- Clients (people icon)
- Reports (analytics icon)
```

### Company B (Sustainability Focus)

```dart
- Home (home icon)
- Dashboard (dashboard icon)
- Sustainability (eco icon)
- Projects (nature icon)
- Impact (assessment icon)
```

### OCIGM (Research Focus)

```dart
- Home (home icon)
- Dashboard (dashboard icon)
- Research (science icon)
- Lab (biotech icon)
- Publications (article icon)
```

## Implementation

### Using ResponsiveScaffold

```dart
ResponsiveScaffold(
  tenantConfig: config,
  title: 'Page Title',
  currentIndex: _currentIndex,
  onNavigationChanged: (index) {
    setState(() => _currentIndex = index);
    // Handle navigation logic
  },
  body: _buildPageContent(),
  floatingActionButton: FloatingActionButton(...),
)
```

### Using ResponsivePageContent

```dart
ResponsivePageContent(
  tenantConfig: config,
  scrollable: true,
  children: [
    // Your page content widgets
  ],
)
```

## Features

### ✅ Automatic Platform Detection

- Detects mobile platforms (iOS/Android)
- Detects web platform
- Adjusts navigation accordingly

### ✅ Screen Size Breakpoints

- **Mobile**: < 600px
- **Tablet**: 600px - 1200px
- **Desktop**: > 1200px

### ✅ Tenant-Specific Theming

- Navigation items use tenant primary colors
- Tenant logo/branding in desktop sidebar
- Consistent theming across all screen sizes

### ✅ Responsive Content

- Automatic content width constraints
- Adaptive padding based on screen size
- Single/multi-column layouts

## Navigation Behavior

### Mobile (Bottom Navigation)

```dart
BottomNavigationBar(
  currentIndex: currentIndex,
  onTap: onNavigationChanged,
  type: BottomNavigationBarType.fixed,
  selectedItemColor: tenantPrimaryColor,
  items: navigationItems.take(5), // Limited to 5 items
)
```

### Tablet (Navigation Rail)

```dart
NavigationRail(
  selectedIndex: currentIndex,
  onDestinationSelected: onNavigationChanged,
  labelType: NavigationRailLabelType.selected,
  selectedIconTheme: IconThemeData(color: tenantPrimaryColor),
  destinations: navigationItems,
)
```

### Desktop (Full Sidebar)

```dart
Container(
  width: 280,
  child: Column(
    children: [
      // Tenant branding header
      // Navigation items list
    ],
  ),
)
```

## Responsive Breakpoint Testing

### Testing Different Screen Sizes

1. **Mobile Simulation**:

   ```bash
   # Use mobile device simulator
   flutter run -d <mobile-device-id>
   ```

2. **Tablet Simulation**:

   ```bash
   # Resize browser window to 800px width
   flutter run -d chrome
   ```

3. **Desktop Simulation**:
   ```bash
   # Use full desktop browser
   flutter run -d chrome
   # Or maximize browser window
   ```

### Chrome DevTools Testing

1. Open Chrome DevTools (F12)
2. Click device toolbar icon
3. Test different device presets:
   - iPhone 12 Pro (390px) → Bottom navigation
   - iPad (768px) → Navigation rail
   - Desktop (1440px) → Full sidebar

## Benefits

### 🎯 User Experience

- **Mobile**: Thumb-friendly bottom navigation
- **Tablet**: Space-efficient rail navigation
- **Desktop**: Full-featured sidebar with branding

### 🔧 Developer Experience

- Single component handles all responsive layouts
- Automatic platform detection
- Tenant-specific navigation items
- Consistent theming system

### 📱 Platform Optimization

- **Mobile**: Touch-optimized interface
- **Web**: Mouse/keyboard optimized
- **Cross-platform**: Consistent experience

## Usage Examples

### Basic Implementation

```dart
class MyPage extends StatefulWidget {
  @override
  Widget build(BuildContext context) {
    return BlocBuilder<TenantBloc, TenantState>(
      builder: (context, state) {
        if (state is TenantLoaded) {
          return ResponsiveScaffold(
            tenantConfig: state.config,
            title: 'My Page',
            body: ResponsivePageContent(
              tenantConfig: state.config,
              children: [
                // Your content here
              ],
            ),
          );
        }
        return LoadingScaffold();
      },
    );
  }
}
```

### Custom Navigation Handling

```dart
void _handleNavigation(int index, TenantConfig config) {
  switch (index) {
    case 0:
      // Navigate to home
      break;
    case 1:
      // Navigate to dashboard
      break;
    default:
      // Handle tenant-specific pages
      _navigateToTenantPage(config, index);
  }
}
```

The responsive navigation system ensures optimal user experience across all devices while maintaining tenant-specific branding and functionality!
