# Multi-Tenant Cross-Platform Flutter Application

A comprehensive Flutter application designed for multi-tenant environments with support for iOS, Android, Windows, macOS, and Web platforms.

## Features

### 🏢 Multi-Tenant Architecture

- **Dynamic Tenant Configuration**: Each tenant can have unique branding, themes, and settings
- **Runtime Tenant Switching**: Switch between tenants without app restart
- **Tenant-Specific Assets**: Logos, images, and resources per tenant
- **Custom Settings**: Tenant-specific configuration options

### 🎨 Advanced Theming

- **Dynamic Theme Generation**: Automatic theme creation from tenant configurations
- **Light/Dark Mode Support**: Full support for both light and dark themes
- **Custom Color Schemes**: Tenant-specific color palettes
- **Typography Customization**: Custom fonts per tenant

### 🌍 Internationalization (i18n)

- **Multi-Language Support**: Currently supports English, Spanish, French, German, Chinese, Japanese, and Arabic
- **Easy Translation Management**: JSON-based translation files
- **Locale-Aware Formatting**: Proper formatting for dates, numbers, etc.
- **RTL Support**: Right-to-left language support

### 🔗 HTTP REST API Client

- **Retrofit Integration**: Type-safe API client generation
- **Error Handling**: Comprehensive error handling with custom exceptions
- **Interceptors**: Request/response logging and authentication
- **Offline Support**: Caching capabilities with Hive

### 🏗️ Clean Architecture

- **Separation of Concerns**: Clear separation between presentation, domain, and data layers
- **Dependency Injection**: Using get_it and injectable for DI
- **State Management**: BLoC pattern for predictable state management
- **Repository Pattern**: Clean data access abstraction

### 📱 Cross-Platform Support

- **iOS**: Native iOS experience
- **Android**: Material Design implementation
- **Web**: Progressive Web App capabilities
- **Windows**: Desktop application support
- **macOS**: Native macOS experience

## Project Structure

```
lib/
├── core/                          # Core functionality
│   ├── error/                     # Error handling
│   ├── injection/                 # Dependency injection
│   ├── network/                   # HTTP client
│   └── storage/                   # Local storage
├── features/                      # Feature modules
│   └── tenant/                    # Tenant management
│       ├── data/                  # Data layer
│       ├── domain/                # Domain layer
│       └── presentation/          # Presentation layer
├── shared/                        # Shared utilities
│   ├── localization/              # i18n support
│   └── theme/                     # Theming system
└── main.dart                      # Application entry point

assets/
├── config/                        # Configuration files
├── images/                        # Images and assets
│   └── tenants/                   # Tenant-specific assets
├── translations/                  # Translation files
├── icons/                         # Icons
└── fonts/                         # Custom fonts
```

## Getting Started

### Prerequisites

- Flutter SDK (3.8.1 or higher)
- Dart SDK
- Platform-specific tools:
  - For iOS: Xcode
  - For Android: Android Studio
  - For Web: Chrome
  - For Desktop: Platform-specific requirements

### Installation

1. **Clone the repository**

   ```bash
   git clone <repository-url>
   cd papp
   ```

2. **Install dependencies**

   ```bash
   flutter pub get
   ```

3. **Generate code**

   ```bash
   flutter packages pub run build_runner build
   ```

4. **Run the application**

   ```bash
   # For development
   flutter run

   # For specific platforms
   flutter run -d chrome        # Web
   flutter run -d windows       # Windows
   flutter run -d macos         # macOS
   ```

### Building for Production

```bash
# Web
flutter build web

# Android
flutter build apk --release
flutter build appbundle --release

# iOS
flutter build ios --release

# Windows
flutter build windows --release

# macOS
flutter build macos --release
```

## Configuration

### Tenant Configuration

Edit `assets/config/tenants.json` to add or modify tenant configurations:

```json
{
  "tenant_id": {
    "id": "tenant_id",
    "name": "tenant_name",
    "displayName": "Display Name",
    "theme": {
      "primaryColors": {
        "primary": "#2196F3",
        "secondary": "#03DAC6"
      }
    },
    "assets": {
      "logo": "logo.png"
    }
  }
}
```

### Adding Translations

Add translation files in `assets/translations/`:

- `en.json` - English
- `es.json` - Spanish
- `fr.json` - French
- etc.

### API Configuration

Update the base URL in `lib/core/injection/injection.dart`:

```dart
dio.options.baseUrl = 'https://your-api-endpoint.com/';
```

## Architecture Details

### Dependency Injection

The application uses `get_it` with `injectable` for dependency injection. All dependencies are configured in `lib/core/injection/injection.dart`.

### State Management

BLoC pattern is used for state management, providing:

- Predictable state changes
- Easy testing
- Separation of business logic from UI

### Data Layer

- **Remote Data Sources**: API clients using Retrofit
- **Local Data Sources**: Local storage using SharedPreferences and Hive
- **Repositories**: Abstract data access with offline support

### Error Handling

Comprehensive error handling with:

- Custom exception types
- Error mapping from data to domain layer
- User-friendly error messages

## Testing

Run tests with:

```bash
flutter test
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions, please create an issue in the repository.

---

**Built with ❤️ using Flutter and Clean Architecture principles**
