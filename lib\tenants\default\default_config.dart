import '../../features/tenant/domain/entities/tenant_config.dart';

/// Default tenant configuration
class DefaultTenantConfig {
  static const TenantConfig config = TenantConfig(
    id: 'default',
    name: 'default',
    displayName: 'Default Tenant',
    theme: TenantTheme(
      primaryColors: TenantColorScheme(
        primary: '#2196F3',
        secondary: '#03DAC6',
        surface: '#FFFFFF',
        background: '#FAFAFA',
        error: '#F44336',
        onPrimary: '#FFFFFF',
        onSecondary: '#000000',
        onSurface: '#000000',
        onBackground: '#000000',
        onError: '#FFFFFF',
      ),
      darkColors: TenantColorScheme(
        primary: '#90CAF9',
        secondary: '#03DAC6',
        surface: '#121212',
        background: '#121212',
        error: '#CF6679',
        onPrimary: '#000000',
        onSecondary: '#000000',
        onSurface: '#FFFFFF',
        onBackground: '#FFFFFF',
        onError: '#000000',
      ),
      fontFamily: 'Roboto',
    ),
    assets: TenantAssets(
      logo: 'assets/images/tenants/default/logo.png',
      logoLight: 'assets/images/tenants/default/logo_light.png',
      logoDark: 'assets/images/tenants/default/logo_dark.png',
      customImages: {
        'hero_banner': 'assets/images/tenants/default/hero_banner.jpg',
        'feature_image': 'assets/images/tenants/default/feature.jpg',
      },
    ),
    layout: TenantLayout(
      layoutType: 'standard',
      showHeader: true,
      showSidebar: false,
      showFooter: true,
      appBarStyle: 'standard',
      navigationStyle: 'bottom',
      cardStyle: 'elevated',
      componentSettings: {'cardElevation': 4, 'borderRadius': 12},
      spacing: {'small': 8, 'medium': 16, 'large': 24},
    ),
    customSettings: {
      'description': 'Standard multi-tenant application',
      'supportEmail': '<EMAIL>',
      'features': ['basic_features', 'standard_ui'],
    },
  );
}
