import 'package:flutter/material.dart';
import '../../features/tenant/domain/entities/tenant_config.dart';
import '../services/responsive_navigation_service.dart';

/// A responsive scaffold that adapts navigation based on screen size and tenant configuration
class ResponsiveScaffold extends StatefulWidget {
  final TenantConfig tenantConfig;
  final Widget body;
  final String? title;
  final List<Widget>? actions;
  final Widget? floatingActionButton;
  final int currentIndex;
  final ValueChanged<int>? onNavigationChanged;

  const ResponsiveScaffold({
    super.key,
    required this.tenantConfig,
    required this.body,
    this.title,
    this.actions,
    this.floatingActionButton,
    this.currentIndex = 0,
    this.onNavigationChanged,
  });

  @override
  State<ResponsiveScaffold> createState() => _ResponsiveScaffoldState();
}

class _ResponsiveScaffoldState extends State<ResponsiveScaffold> {
  late List<NavigationItem> navigationItems;
  bool _isNavigationCollapsed = false;

  @override
  void initState() {
    super.initState();
    navigationItems = ResponsiveNavigationService.getNavigationItems(
      widget.tenantConfig,
    );
  }

  void _toggleNavigation() {
    setState(() {
      _isNavigationCollapsed = !_isNavigationCollapsed;
    });
  }

  @override
  Widget build(BuildContext context) {
    final layoutConfig = ResponsiveNavigationService.getLayoutConfig(
      context,
      widget.tenantConfig,
    );

    return LayoutBuilder(
      builder: (context, constraints) {
        switch (layoutConfig.navigationStyle) {
          case NavigationStyle.sideNavigation:
            return _buildDesktopLayout(context, layoutConfig);
          case NavigationStyle.railNavigation:
            return _buildTabletLayout(context, layoutConfig);
          case NavigationStyle.bottomNavigation:
            return _buildMobileLayout(context, layoutConfig);
        }
      },
    );
  }

  /// Desktop layout with full sidebar navigation
  Widget _buildDesktopLayout(
    BuildContext context,
    ResponsiveLayoutConfig config,
  ) {
    return Scaffold(
      body: Row(
        children: [
          _buildSideNavigation(context),
          Expanded(
            child: Column(
              children: [
                if (widget.title != null) _buildAppBar(context),
                Expanded(
                  child: Container(
                    constraints: BoxConstraints(
                      maxWidth: config.maxContentWidth,
                    ),
                    padding: config.padding,
                    child: widget.body,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: widget.floatingActionButton,
    );
  }

  /// Tablet layout with navigation rail
  Widget _buildTabletLayout(
    BuildContext context,
    ResponsiveLayoutConfig config,
  ) {
    return Scaffold(
      appBar: widget.title != null ? _buildAppBar(context) : null,
      body: Row(
        children: [
          _buildNavigationRail(context),
          Expanded(
            child: Container(
              constraints: BoxConstraints(maxWidth: config.maxContentWidth),
              padding: config.padding,
              child: widget.body,
            ),
          ),
        ],
      ),
      floatingActionButton: widget.floatingActionButton,
    );
  }

  /// Mobile layout with bottom navigation
  Widget _buildMobileLayout(
    BuildContext context,
    ResponsiveLayoutConfig config,
  ) {
    return Scaffold(
      appBar: widget.title != null ? _buildAppBar(context) : null,
      body: Container(padding: config.padding, child: widget.body),
      bottomNavigationBar: _buildBottomNavigation(context),
      floatingActionButton: widget.floatingActionButton,
    );
  }

  /// Build app bar with tenant-specific theming
  PreferredSizeWidget _buildAppBar(BuildContext context) {
    return AppBar(
      leading: IconButton(
        icon: Icon(_isNavigationCollapsed ? Icons.menu : Icons.menu_open),
        onPressed: _toggleNavigation,
      ),
      title: Text(widget.title ?? ''),
      actions: widget.actions,
      backgroundColor: _getTenantPrimaryColor(),
      foregroundColor: Colors.white,
      elevation: 0,
    );
  }

  /// Build full sidebar navigation for desktop
  Widget _buildSideNavigation(BuildContext context) {
    final sidebarWidth = _isNavigationCollapsed ? 88.0 : 280.0;

    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      width: sidebarWidth,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        border: Border(
          right: BorderSide(
            color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
          ),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Logo/Brand area with same height as app bar
          Container(
            height: kToolbarHeight,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              children: [
                Icon(Icons.business, color: _getTenantPrimaryColor(), size: 24),
                if (!_isNavigationCollapsed) ...[
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      widget.tenantConfig.displayName,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: _getTenantPrimaryColor(),
                        fontWeight: FontWeight.bold,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ],
            ),
          ),
          const Divider(),
          // Navigation items
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(vertical: 8),
              itemCount: navigationItems.length,
              itemBuilder: (context, index) {
                final item = navigationItems[index];
                final isSelected = index == widget.currentIndex;

                if (_isNavigationCollapsed) {
                  return Container(
                    margin: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 4,
                    ),
                    child: Tooltip(
                      message: item.label,
                      child: IconButton(
                        icon: Icon(
                          item.icon,
                          color: isSelected
                              ? _getTenantPrimaryColor()
                              : Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                        onPressed: () =>
                            widget.onNavigationChanged?.call(index),
                        style: IconButton.styleFrom(
                          backgroundColor: isSelected
                              ? _getTenantPrimaryColor().withOpacity(0.1)
                              : null,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                      ),
                    ),
                  );
                }

                return Container(
                  margin: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 2,
                  ),
                  child: _isNavigationCollapsed
                      ? Tooltip(
                          message: item.label,
                          child: GestureDetector(
                            onTap: () =>
                                widget.onNavigationChanged?.call(index),
                            child: Container(
                              width: double.infinity,
                              height: 48,
                              decoration: BoxDecoration(
                                color: isSelected
                                    ? _getTenantPrimaryColor().withOpacity(0.1)
                                    : null,
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Center(
                                child: Icon(
                                  item.icon,
                                  size: 24,
                                  color: isSelected
                                      ? _getTenantPrimaryColor()
                                      : Theme.of(
                                          context,
                                        ).colorScheme.onSurfaceVariant,
                                ),
                              ),
                            ),
                          ),
                        )
                      : ListTile(
                          dense: true,
                          contentPadding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 4,
                          ),
                          leading: Icon(
                            item.icon,
                            size: 20,
                            color: isSelected
                                ? _getTenantPrimaryColor()
                                : Theme.of(
                                    context,
                                  ).colorScheme.onSurfaceVariant,
                          ),
                          title: Text(
                            item.label,
                            style: TextStyle(
                              fontSize: 14,
                              color: isSelected
                                  ? _getTenantPrimaryColor()
                                  : Theme.of(
                                      context,
                                    ).colorScheme.onSurfaceVariant,
                              fontWeight: isSelected
                                  ? FontWeight.w600
                                  : FontWeight.normal,
                            ),
                          ),
                          selected: isSelected,
                          selectedTileColor: _getTenantPrimaryColor()
                              .withOpacity(0.1),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          onTap: () => widget.onNavigationChanged?.call(index),
                        ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  /// Build navigation rail for tablet
  Widget _buildNavigationRail(BuildContext context) {
    return NavigationRail(
      selectedIndex: widget.currentIndex,
      onDestinationSelected: widget.onNavigationChanged,
      labelType: NavigationRailLabelType.selected,
      backgroundColor: Theme.of(context).colorScheme.surfaceContainerHighest,
      selectedIconTheme: IconThemeData(color: _getTenantPrimaryColor()),
      selectedLabelTextStyle: TextStyle(color: _getTenantPrimaryColor()),
      destinations: navigationItems.map((item) {
        return NavigationRailDestination(
          icon: Icon(item.icon),
          label: Text(item.label),
        );
      }).toList(),
    );
  }

  /// Build bottom navigation for mobile
  Widget _buildBottomNavigation(BuildContext context) {
    // Limit to 5 items for bottom navigation (Material Design guideline)
    final limitedItems = navigationItems.take(5).toList();

    return BottomNavigationBar(
      currentIndex: widget.currentIndex.clamp(0, limitedItems.length - 1),
      onTap: widget.onNavigationChanged,
      type: BottomNavigationBarType.fixed,
      selectedItemColor: _getTenantPrimaryColor(),
      unselectedItemColor: Theme.of(context).colorScheme.onSurfaceVariant,
      items: limitedItems.map((item) {
        return BottomNavigationBarItem(
          icon: Icon(item.icon),
          label: item.label,
        );
      }).toList(),
    );
  }

  /// Get tenant-specific primary color
  Color _getTenantPrimaryColor() {
    try {
      final colorString = widget.tenantConfig.theme.primaryColors.primary;
      return Color(int.parse(colorString.replaceFirst('#', '0xFF')));
    } catch (e) {
      return Theme.of(context).colorScheme.primary;
    }
  }
}

/// Helper widget for responsive page content
class ResponsivePageContent extends StatelessWidget {
  final TenantConfig tenantConfig;
  final List<Widget> children;
  final CrossAxisAlignment crossAxisAlignment;
  final MainAxisAlignment mainAxisAlignment;
  final bool scrollable;

  const ResponsivePageContent({
    super.key,
    required this.tenantConfig,
    required this.children,
    this.crossAxisAlignment = CrossAxisAlignment.start,
    this.mainAxisAlignment = MainAxisAlignment.start,
    this.scrollable = true,
  });

  @override
  Widget build(BuildContext context) {
    final layoutConfig = ResponsiveNavigationService.getLayoutConfig(
      context,
      tenantConfig,
    );

    Widget content = Column(
      crossAxisAlignment: crossAxisAlignment,
      mainAxisAlignment: mainAxisAlignment,
      children: children,
    );

    if (scrollable) {
      content = SingleChildScrollView(child: content);
    }

    // Apply responsive constraints
    if (layoutConfig.maxContentWidth != double.infinity) {
      content = Center(
        child: ConstrainedBox(
          constraints: BoxConstraints(maxWidth: layoutConfig.maxContentWidth),
          child: content,
        ),
      );
    }

    return Padding(padding: layoutConfig.padding, child: content);
  }
}
