import 'dart:convert';
import '../models/tenant_config_model.dart';
import '../../../../core/storage/storage_service.dart';
import '../../../../core/error/exceptions.dart';

abstract class TenantLocalDataSource {
  Future<TenantConfigModel?> getCachedTenantConfig(String tenantId);
  Future<void> cacheTenantConfig(TenantConfigModel config);
  Future<String?> getCurrentTenantId();
  Future<void> setCurrentTenantId(String tenantId);
  Future<void> clearCache();
}

class TenantLocalDataSourceImpl implements TenantLocalDataSource {
  final StorageService storageService;

  TenantLocalDataSourceImpl(this.storageService);

  @override
  Future<TenantConfigModel?> getCachedTenantConfig(String tenantId) async {
    try {
      final configJson = storageService.getString('tenant_config_$tenantId');
      if (configJson != null) {
        return TenantConfigModel.fromJson(
          Map<String, dynamic>.from(json.decode(configJson)),
        );
      }
      return null;
    } catch (e) {
      throw CacheException('Failed to get cached tenant config: $e');
    }
  }

  @override
  Future<void> cacheTenantConfig(TenantConfigModel config) async {
    try {
      final configJson = json.encode(config.toJson());
      await storageService.setString('tenant_config_${config.id}', configJson);
    } catch (e) {
      throw CacheException('Failed to cache tenant config: $e');
    }
  }

  @override
  Future<String?> getCurrentTenantId() async {
    return storageService.getCurrentTenant();
  }

  @override
  Future<void> setCurrentTenantId(String tenantId) async {
    await storageService.setCurrentTenant(tenantId);
  }

  @override
  Future<void> clearCache() async {
    // Clear tenant-related cache
    final currentTenant = await getCurrentTenantId();
    if (currentTenant != null) {
      await storageService.remove('tenant_config_$currentTenant');
    }
    await storageService.clearCurrentTenant();
  }
}
