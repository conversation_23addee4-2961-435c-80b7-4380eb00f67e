import 'package:flutter/material.dart';
import '../../features/tenant/domain/entities/tenant_config.dart';
import '../../shared/widgets/user_info_card.dart';

/// Default widgets for generic tenant configurations
class DefaultWidgets {
  /// Default home page content
  static Widget buildHomePage(BuildContext context, TenantConfig config) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // User info section
          CurrentUserInfoCard(
            tenantConfig: config,
            isCompact: false,
            onProfileTap: () => _showFeatureDialog(
              context,
              'User Profile',
              'View and edit your profile information',
            ),
            onSettingsTap: () => _showFeatureDialog(
              context,
              'Settings',
              'Manage your account settings',
            ),
          ),
          const SizedBox(height: 24),

          // General Welcome Section
          _buildWelcomeSection(context, config),
          const SizedBox(height: 24),

          // Features Overview
          _buildFeaturesOverview(context, config),
          const SizedBox(height: 24),

          // General Dashboard
          _buildGeneralDashboard(context, config),
          const SizedBox(height: 24),

          // Quick Actions
          _buildQuickActions(context, config),
        ],
      ),
    );
  }

  /// General welcome section
  static Widget _buildWelcomeSection(
    BuildContext context,
    TenantConfig config,
  ) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            _parseColor(config.theme.primaryColors.primary),
            _parseColor(
              config.theme.primaryColors.secondary ??
                  config.theme.primaryColors.primary,
            ),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.apps, color: Colors.white, size: 32),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Welcome to ${config.displayName}',
                      style: Theme.of(context).textTheme.headlineSmall
                          ?.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                    ),
                    Text(
                      'Your Multi-Tenant Application Platform',
                      style: Theme.of(
                        context,
                      ).textTheme.bodyMedium?.copyWith(color: Colors.white70),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Text(
            'Experience a powerful, flexible platform designed to adapt to your unique needs and requirements.',
            style: Theme.of(
              context,
            ).textTheme.bodyLarge?.copyWith(color: Colors.white),
          ),
        ],
      ),
    );
  }

  /// Features overview section
  static Widget _buildFeaturesOverview(
    BuildContext context,
    TenantConfig config,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Platform Features',
          style: Theme.of(
            context,
          ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                _buildFeatureItem(
                  context,
                  'Multi-Tenant Architecture',
                  'Secure isolation with shared infrastructure',
                  Icons.security,
                  config,
                ),
                const Divider(),
                _buildFeatureItem(
                  context,
                  'Responsive Design',
                  'Optimized for desktop, tablet, and mobile',
                  Icons.devices,
                  config,
                ),
                const Divider(),
                _buildFeatureItem(
                  context,
                  'Custom Themes',
                  'Brand-specific colors and styling',
                  Icons.palette,
                  config,
                ),
                const Divider(),
                _buildFeatureItem(
                  context,
                  'Internationalization',
                  'Multiple language support',
                  Icons.language,
                  config,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  /// General dashboard metrics
  static Widget _buildGeneralDashboard(
    BuildContext context,
    TenantConfig config,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Dashboard Overview',
          style: Theme.of(
            context,
          ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: MediaQuery.of(context).size.width > 600 ? 4 : 2,
          mainAxisSpacing: 16,
          crossAxisSpacing: 16,
          childAspectRatio: 1.2,
          children: [
            _buildMetricCard(
              context,
              'Users',
              '1,234',
              Icons.people,
              config,
              Colors.blue,
            ),
            _buildMetricCard(
              context,
              'Sessions',
              '5,678',
              Icons.timeline,
              config,
              Colors.green,
            ),
            _buildMetricCard(
              context,
              'Data',
              '89GB',
              Icons.storage,
              config,
              Colors.orange,
            ),
            _buildMetricCard(
              context,
              'Uptime',
              '99.9%',
              Icons.check_circle,
              config,
              Colors.purple,
            ),
          ],
        ),
      ],
    );
  }

  /// Quick actions section
  static Widget _buildQuickActions(BuildContext context, TenantConfig config) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Quick Actions',
          style: Theme.of(
            context,
          ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        Wrap(
          spacing: 12,
          runSpacing: 12,
          children: [
            _buildActionButton(
              context,
              'Dashboard',
              Icons.dashboard,
              config,
              () => _showFeatureDialog(
                context,
                'Dashboard',
                'Navigate to your main dashboard',
              ),
            ),
            _buildActionButton(
              context,
              'Settings',
              Icons.settings,
              config,
              () => _showFeatureDialog(
                context,
                'Settings',
                'Configure application settings',
              ),
            ),
            _buildActionButton(
              context,
              'Help',
              Icons.help,
              config,
              () => _showFeatureDialog(
                context,
                'Help',
                'Access help and documentation',
              ),
            ),
            _buildActionButton(
              context,
              'Support',
              Icons.support_agent,
              config,
              () => _showFeatureDialog(
                context,
                'Support',
                'Contact our support team',
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// Build feature item
  static Widget _buildFeatureItem(
    BuildContext context,
    String title,
    String description,
    IconData icon,
    TenantConfig config,
  ) {
    return ListTile(
      leading: Icon(
        icon,
        color: _parseColor(config.theme.primaryColors.primary),
        size: 32,
      ),
      title: Text(title, style: const TextStyle(fontWeight: FontWeight.w600)),
      subtitle: Text(description),
      contentPadding: EdgeInsets.zero,
    );
  }

  /// Build metric card
  static Widget _buildMetricCard(
    BuildContext context,
    String title,
    String value,
    IconData icon,
    TenantConfig config,
    Color color,
  ) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, size: 32, color: color),
            const SizedBox(height: 8),
            Text(
              value,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            Text(
              title,
              style: Theme.of(context).textTheme.bodySmall,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// Build action button
  static Widget _buildActionButton(
    BuildContext context,
    String label,
    IconData icon,
    TenantConfig config,
    VoidCallback onPressed,
  ) {
    return ElevatedButton.icon(
      onPressed: onPressed,
      icon: Icon(icon, size: 20),
      label: Text(label),
      style: ElevatedButton.styleFrom(
        backgroundColor: _parseColor(config.theme.primaryColors.primary),
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      ),
    );
  }

  /// Show feature dialog
  static void _showFeatureDialog(
    BuildContext context,
    String title,
    String description,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(description),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  /// Parse color from hex string
  static Color _parseColor(String hexString) {
    try {
      return Color(int.parse(hexString.replaceFirst('#', '0xFF')));
    } catch (e) {
      return Colors.blue; // Fallback color
    }
  }
}
