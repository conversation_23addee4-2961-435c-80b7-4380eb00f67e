import 'dart:convert';
import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';

class AppLocalizations {
  final Locale locale;
  late Map<String, String> _localizedStrings;

  AppLocalizations(this.locale);

  static AppLocalizations of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations)!;
  }

  static const LocalizationsDelegate<AppLocalizations> delegate =
      _AppLocalizationsDelegate();

  Future<bool> load() async {
    // Load the language JSON file from assets based on the locale
    String jsonString = await rootBundle.loadString(
      'assets/translations/${locale.languageCode}.json',
    );
    Map<String, dynamic> jsonMap = json.decode(jsonString);

    _localizedStrings = jsonMap.map((key, value) {
      return MapEntry(key, value.toString());
    });

    return true;
  }

  String translate(String key) {
    return _localizedStrings[key] ?? key;
  }

  // Helper method to get text with fallback
  String t(String key, [String? fallback]) {
    return _localizedStrings[key] ?? fallback ?? key;
  }

  // Helper method for plural forms
  String plural(String key, int count, [String? fallback]) {
    String pluralKey = count == 1 ? '${key}_one' : '${key}_other';
    return _localizedStrings[pluralKey] ??
        _localizedStrings[key] ??
        fallback ??
        key;
  }

  // Common app strings
  String get appTitle => translate('app_title');
  String get loading => translate('loading');
  String get error => translate('error');
  String get retry => translate('retry');
  String get cancel => translate('cancel');
  String get ok => translate('ok');
  String get yes => translate('yes');
  String get no => translate('no');
  String get save => translate('save');
  String get delete => translate('delete');
  String get edit => translate('edit');
  String get settings => translate('settings');
  String get logout => translate('logout');
  String get login => translate('login');
  String get welcome => translate('welcome');
  String get searchHint => translate('search_hint');
  String get noResultsFound => translate('no_results_found');
  String get internetConnectionError => translate('internet_connection_error');
  String get serverError => translate('server_error');
  String get unauthorizedError => translate('unauthorized_error');
  String get notFoundError => translate('not_found_error');
  String get validationError => translate('validation_error');
}

class _AppLocalizationsDelegate
    extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  bool isSupported(Locale locale) {
    // Define which locales are supported
    return [
      'en',
      'es',
      'fr',
      'de',
      'zh',
      'ja',
      'ar',
    ].contains(locale.languageCode);
  }

  @override
  Future<AppLocalizations> load(Locale locale) async {
    AppLocalizations localizations = AppLocalizations(locale);
    await localizations.load();
    return localizations;
  }

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

// Extension for easy access
extension LocalizationExtension on BuildContext {
  AppLocalizations get localizations => AppLocalizations.of(this);
  String t(String key, [String? fallback]) => localizations.t(key, fallback);
  String plural(String key, int count, [String? fallback]) =>
      localizations.plural(key, count, fallback);
}
