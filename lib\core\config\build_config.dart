import 'dart:io' show Platform;
import 'package:flutter/foundation.dart' show kIsWeb;

/// Build-time tenant configuration
/// This class determines which tenant to build for based on environment variables
class BuildConfig {
  static const String _defaultTenant = 'default';

  /// Get the tenant ID from environment variable or use default
  static String get tenantId {
    // Use const String.fromEnvironment for web compatibility
    const tenantFromEnvironment = String.fromEnvironment('TENANT_ID');

    if (tenantFromEnvironment.isNotEmpty) {
      return tenantFromEnvironment;
    }

    // Fallback to Platform.environment for non-web platforms
    if (!kIsWeb) {
      try {
        return Platform.environment['TENANT_ID'] ?? _defaultTenant;
      } catch (e) {
        // If Platform.environment fails, use default
        return _defaultTenant;
      }
    }

    return _defaultTenant;
  }

  /// Get the app name based on tenant
  static String get appName {
    switch (tenantId) {
      case 'company_a':
        return 'Company A Business App';
      case 'company_b':
        return 'EcoTech Solutions';
      case 'ocigm':
        return 'OCIGM Innovation Hub';
      default:
        return 'Multi-Tenant App';
    }
  }

  /// Get the app identifier for platform-specific builds
  static String get appId {
    switch (tenantId) {
      case 'company_a':
        return 'com.company_a.businessapp';
      case 'company_b':
        return 'com.company_b.ecotech';
      case 'ocigm':
        return 'com.ocigm.innovation';
      default:
        return 'com.example.multitenantapp';
    }
  }

  /// Check if this is a debug build
  static bool get isDebugBuild {
    bool inDebugMode = false;
    assert(inDebugMode = true);
    return inDebugMode;
  }

  /// Get build configuration info
  static Map<String, String> get buildInfo {
    return {
      'tenantId': tenantId,
      'appName': appName,
      'appId': appId,
      'buildMode': isDebugBuild ? 'debug' : 'release',
      'buildTime': DateTime.now().toIso8601String(),
    };
  }
}
