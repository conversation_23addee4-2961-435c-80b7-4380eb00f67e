import 'package:flutter/material.dart';
import '../../features/tenant/domain/entities/tenant_config.dart';
import '../services/tenant_asset_service.dart';

/// Common user info card component used by all tenants
/// This widget displays user information in a consistent way across all tenants
/// while respecting tenant-specific theming and layout preferences
class UserInfoCard extends StatelessWidget {
  final TenantConfig tenantConfig;
  final String userName;
  final String userEmail;
  final String? userRole;
  final String? userDepartment;
  final String? avatarUrl;
  final VoidCallback? onTap;
  final VoidCallback? onEditProfile;
  final bool isCompact;
  final bool showActions;

  const UserInfoCard({
    super.key,
    required this.tenantConfig,
    required this.userName,
    required this.userEmail,
    this.userRole,
    this.userDepartment,
    this.avatarUrl,
    this.onTap,
    this.onEditProfile,
    this.isCompact = false,
    this.showActions = true,
  });

  @override
  Widget build(BuildContext context) {
    return TenantAwareCard(
      tenantConfig: tenantConfig,
      onTap: onTap,
      child: Padding(
        padding: EdgeInsets.all(isCompact ? 12.0 : 16.0),
        child: isCompact
            ? _buildCompactLayout(context)
            : _buildStandardLayout(context),
      ),
    );
  }

  Widget _buildStandardLayout(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            _buildAvatar(context),
            const SizedBox(width: 16),
            Expanded(child: _buildUserDetails(context)),
            if (showActions) _buildActionButton(context),
          ],
        ),
        if (userDepartment != null) ...[
          const SizedBox(height: 12),
          _buildDepartmentInfo(context),
        ],
      ],
    );
  }

  Widget _buildCompactLayout(BuildContext context) {
    return Row(
      children: [
        _buildAvatar(context, isSmall: true),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                userName,
                style: Theme.of(
                  context,
                ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              if (userRole != null)
                Text(
                  userRole!,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.primary,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
            ],
          ),
        ),
        if (showActions) _buildActionButton(context, isSmall: true),
      ],
    );
  }

  Widget _buildAvatar(BuildContext context, {bool isSmall = false}) {
    final size = isSmall ? 40.0 : 56.0;
    final fontSize = isSmall ? 16.0 : 20.0;

    if (avatarUrl != null && avatarUrl!.isNotEmpty) {
      return CircleAvatar(
        radius: size / 2,
        backgroundImage: NetworkImage(avatarUrl!),
        onBackgroundImageError: (_, __) {
          // Fallback to initials if image fails to load
        },
        child: avatarUrl!.isEmpty ? _buildInitials(context, fontSize) : null,
      );
    }

    return CircleAvatar(
      radius: size / 2,
      backgroundColor: _getPrimaryColor(context).withOpacity(0.1),
      child: _buildInitials(context, fontSize),
    );
  }

  Widget _buildInitials(BuildContext context, double fontSize) {
    final initials = _getInitials(userName);
    return Text(
      initials,
      style: TextStyle(
        fontSize: fontSize,
        fontWeight: FontWeight.bold,
        color: _getPrimaryColor(context),
      ),
    );
  }

  Widget _buildUserDetails(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          userName,
          style: Theme.of(
            context,
          ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.w600),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        const SizedBox(height: 4),
        Text(
          userEmail,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        if (userRole != null) ...[
          const SizedBox(height: 4),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
            decoration: BoxDecoration(
              color: _getPrimaryColor(context).withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              userRole!,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: _getPrimaryColor(context),
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildDepartmentInfo(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Theme.of(
          context,
        ).colorScheme.surfaceContainerHighest.withOpacity(0.3),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.business,
            size: 16,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
          const SizedBox(width: 8),
          Text(
            'Department: $userDepartment',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton(BuildContext context, {bool isSmall = false}) {
    if (onEditProfile == null) return const SizedBox.shrink();

    if (isSmall) {
      return IconButton(
        onPressed: onEditProfile,
        icon: const Icon(Icons.edit),
        iconSize: 20,
        constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
        padding: EdgeInsets.zero,
      );
    }

    return Column(
      children: [
        IconButton.outlined(
          onPressed: onEditProfile,
          icon: const Icon(Icons.edit),
          style: IconButton.styleFrom(
            foregroundColor: _getPrimaryColor(context),
            side: BorderSide(color: _getPrimaryColor(context)),
          ),
        ),
        const SizedBox(height: 4),
        Text(
          'Edit',
          style: Theme.of(
            context,
          ).textTheme.bodySmall?.copyWith(color: _getPrimaryColor(context)),
        ),
      ],
    );
  }

  Color _getPrimaryColor(BuildContext context) {
    try {
      final colorString = tenantConfig.theme.primaryColors.primary;
      return Color(int.parse(colorString.replaceFirst('#', '0xFF')));
    } catch (e) {
      return Theme.of(context).colorScheme.primary;
    }
  }

  String _getInitials(String name) {
    final words = name.trim().split(' ');
    if (words.length == 1) {
      return words[0].substring(0, 1).toUpperCase();
    }
    return (words[0].substring(0, 1) + words[1].substring(0, 1)).toUpperCase();
  }
}

/// Preset user info card for current user display
class CurrentUserInfoCard extends StatelessWidget {
  final TenantConfig tenantConfig;
  final VoidCallback? onProfileTap;
  final VoidCallback? onSettingsTap;
  final bool isCompact;

  const CurrentUserInfoCard({
    super.key,
    required this.tenantConfig,
    this.onProfileTap,
    this.onSettingsTap,
    this.isCompact = false,
  });

  @override
  Widget build(BuildContext context) {
    // Mock current user data - in real app, this would come from auth service
    return UserInfoCard(
      tenantConfig: tenantConfig,
      userName: _getCurrentUserName(),
      userEmail: _getCurrentUserEmail(),
      userRole: _getCurrentUserRole(),
      userDepartment: _getCurrentUserDepartment(),
      avatarUrl: _getCurrentUserAvatar(),
      onTap: onProfileTap,
      onEditProfile: onSettingsTap,
      isCompact: isCompact,
      showActions: true,
    );
  }

  String _getCurrentUserName() {
    switch (tenantConfig.id) {
      case 'company_a':
        return 'John Doe';
      case 'company_b':
        return 'Sarah Green';
      case 'ocigm':
        return 'Dr. Alex Chen';
      default:
        return 'Demo User';
    }
  }

  String _getCurrentUserEmail() {
    switch (tenantConfig.id) {
      case 'company_a':
        return '<EMAIL>';
      case 'company_b':
        return '<EMAIL>';
      case 'ocigm':
        return '<EMAIL>';
      default:
        return '<EMAIL>';
    }
  }

  String _getCurrentUserRole() {
    switch (tenantConfig.id) {
      case 'company_a':
        return 'Project Manager';
      case 'company_b':
        return 'Sustainability Lead';
      case 'ocigm':
        return 'Research Director';
      default:
        return 'User';
    }
  }

  String? _getCurrentUserDepartment() {
    switch (tenantConfig.id) {
      case 'company_a':
        return 'Business Development';
      case 'company_b':
        return 'Environmental Solutions';
      case 'ocigm':
        return 'Innovation Research';
      default:
        return null;
    }
  }

  String? _getCurrentUserAvatar() {
    // In a real app, this would return actual avatar URLs
    return null; // Using initials instead
  }
}
