import '../../features/tenant/domain/entities/tenant_config.dart';

/// Company B tenant configuration - Eco-friendly sidebar layout
class CompanyBTenantConfig {
  static const TenantConfig config = TenantConfig(
    id: 'company_b',
    name: 'company_b',
    displayName: 'Company B',
    theme: TenantTheme(
      primaryColors: TenantColorScheme(
        primary: '#4CAF50',
        secondary: '#8BC34A',
        surface: '#FFFFFF',
        background: '#F1F8E9',
        error: '#F44336',
        onPrimary: '#FFFFFF',
        onSecondary: '#000000',
        onSurface: '#000000',
        onBackground: '#000000',
        onError: '#FFFFFF',
      ),
      fontFamily: 'Open Sans',
    ),
    assets: TenantAssets(
      logo: 'assets/images/tenants/company_b/company_b_logo.png',
      logoLight: 'assets/images/tenants/company_b/company_b_logo_light.png',
      logoDark: 'assets/images/tenants/company_b/company_b_logo_dark.png',
      customImages: {
        'hero_banner': 'assets/images/tenants/company_b/nature_banner.jpg',
        'feature_image': 'assets/images/tenants/company_b/eco_feature.jpg',
        'sustainability_image':
            'assets/images/tenants/company_b/sustainability.jpg',
        'green_office': 'assets/images/tenants/company_b/green_office.jpg',
      },
    ),
    layout: TenantLayout(
      layoutType: 'sidebar',
      showHeader: true,
      showSidebar: true,
      showFooter: true,
      appBarStyle: 'prominent',
      navigationStyle: 'rail',
      cardStyle: 'filled',
      componentSettings: {
        'cardElevation': 0,
        'borderRadius': 12,
        'sidebarWidth': 280,
        'useNaturalColors': true,
      },
      spacing: {'small': 12, 'medium': 20, 'large': 28},
    ),
    customSettings: {
      'companyType': 'eco-friendly',
      'industry': 'sustainability',
      'supportEmail': '<EMAIL>',
      'features': ['eco_features', 'sidebar_ui', 'sustainability_tracking'],
      'brandColors': ['#4CAF50', '#8BC34A'],
      'motto': 'Green Technology for a Better Future',
      'certifications': ['ISO 14001', 'LEED Certified'],
    },
  );
}
