# Android Development Setup Guide

## Quick Setup for Testing Multi-Tenant App on Android

### Step 1: Install Android Studio

1. Download Android Studio from: https://developer.android.com/studio
2. Install with default settings (includes Android SDK)
3. Accept Android licenses during installation

### Step 2: Set up Android Virtual Device (AVD)

**Option A: Using Android Studio (Recommended)**

1. Open Android Studio
2. Click "More Actions" → "AVD Manager" (or Tools → AVD Manager if project is open)
3. Click "Create Virtual Device"
4. Select device definition (recommended: Pixel 7)
5. Download system image (recommended: API 34 - Android 14)
6. Configure AVD settings:
   - Name: `Pixel_7_API_34`
   - Graphics: Hardware - GLES 2.0
   - RAM: 4096 MB (if available)
7. Click "Finish"

**Option B: Using Command Line**

```bash
# List available system images
sdkmanager --list | findstr "system-images"

# Download system image
sdkmanager "system-images;android-34;google_apis;x86_64"

# Create AVD
avdmanager create avd -n Pixel_7_API_34 -k "system-images;android-34;google_apis;x86_64"
```

### Step 3: Verify Flutter Setup

```bash
# Check Flutter setup
flutter doctor

# Should show:
# ✓ Flutter (Channel stable, 3.16.x)
# ✓ Windows Version (Windows 10 or later)
# ✓ Android toolchain - develop for Android devices
# ✓ Chrome - develop for the web
# ✓ Visual Studio - develop Windows apps
# ✓ Android Studio (version 2022.x)
# ✓ VS Code (version 1.x)

# Accept Android licenses if needed
flutter doctor --android-licenses
```

### Step 4: Start Android Emulator and Run App

```bash
# List available emulators
flutter emulators

# Start emulator
flutter emulators --launch Pixel_7_API_34

# Wait for emulator to boot completely, then run app
scripts\run-tenant.bat company_a android
```

### Testing Different Tenants on Android

```bash
# Test Company A tenant
scripts\run-tenant.bat company_a android

# Test Company B tenant
scripts\run-tenant.bat company_b android

# Test OCIGM tenant
scripts\run-tenant.bat ocigm android

# Test default tenant
scripts\run-tenant.bat default android
```

### What You'll See on Android

- **Mobile-Optimized Layout**: Bottom navigation bar instead of sidebar
- **Touch-Friendly UI**: Larger buttons and touch targets
- **Single-Column Layout**: Optimized for mobile screens
- **Tenant-Specific Theming**: Colors and branding per tenant
- **Responsive Components**: UserInfoCard adapts to mobile size

### Troubleshooting

#### "No devices found"

```bash
# Check if emulator is running
flutter devices

# If no Android devices shown, start emulator
flutter emulators --launch Pixel_7_API_34
```

#### "Android toolchain not found"

```bash
# Run Flutter doctor to see issues
flutter doctor

# Usually need to accept licenses
flutter doctor --android-licenses
```

#### "Emulator won't start"

- Ensure Windows Hyper-V is disabled (if using Intel HAXM)
- Enable virtualization in BIOS
- Try creating emulator with different API level
- Restart Android Studio

#### "App crashes on startup"

```bash
# Clean and rebuild
flutter clean
flutter pub get
flutter run -d android --dart-define=TENANT_ID=company_a
```

### Performance Tips

- Use x86_64 system images for better performance
- Allocate 4GB+ RAM to emulator if available
- Enable hardware acceleration in emulator settings
- Close other resource-intensive applications while running emulator

### Next Steps

Once you have Android working:

1. Test all tenant configurations on mobile
2. Verify responsive navigation behavior
3. Test UserInfoCard component on different screen sizes
4. Compare mobile vs desktop layouts
