@echo off
:: Quick run script for development with tenant selection
:: Usage: run-tenant.bat [tenant-id] [device]
:: Example: run-tenant.bat ocigm chrome

setlocal enabledelayedexpansion

:: Default values
set "TENANT_ID=default"
set "DEVICE=chrome"

:: Parse command line arguments
if not "%~1"=="" set "TENANT_ID=%~1"
if not "%~2"=="" set "DEVICE=%~2"

:: Available tenants
set "AVAILABLE_TENANTS=default company_a company_b ocigm"

:: Validate tenant ID
echo %AVAILABLE_TENANTS% | findstr /C:"%TENANT_ID%" >nul
if errorlevel 1 (
    echo Error: Invalid tenant ID '%TENANT_ID%'
    echo Available tenants: %AVAILABLE_TENANTS%
    exit /b 1
)

echo ============================================
echo Running Multi-Tenant Flutter App
echo ============================================
echo Tenant: %TENANT_ID%
echo Device: %DEVICE%
echo ============================================

:: Check for Android device and handle emulator
if "%DEVICE%"=="android" (
    echo Checking for Android devices...
    flutter devices | findstr "android" >nul
    if errorlevel 1 (
        echo No Android devices found. Checking available emulators...
        flutter emulators
        echo.
        echo Please start an Android emulator first using:
        echo   flutter emulators --launch ^<emulator_name^>
        echo.
        echo Then run this script again.
        exit /b 1
    )
)

:: Run the app with tenant configuration
flutter run -d %DEVICE% --dart-define=TENANT_ID=%TENANT_ID%

endlocal
