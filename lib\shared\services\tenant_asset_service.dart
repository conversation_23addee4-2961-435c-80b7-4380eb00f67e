import 'package:flutter/material.dart';
import '../../features/tenant/domain/entities/tenant_config.dart';

/// Service for managing tenant-specific assets and layouts
class TenantAssetService {
  static const String _defaultImagePath = 'assets/images/default/';

  /// Get the tenant logo based on current theme brightness
  static String getTenantLogo(TenantConfig config, {bool isDark = false}) {
    if (isDark && config.assets.logoDark != null) {
      return config.assets.logoDark!;
    } else if (!isDark && config.assets.logoLight != null) {
      return config.assets.logoLight!;
    } else if (config.assets.logo != null) {
      return config.assets.logo!;
    }
    return '${_defaultImagePath}logo.png';
  }

  /// Get a custom image for the tenant
  static String getTenantImage(
    TenantConfig config,
    String imageKey, {
    String? fallback,
  }) {
    if (config.assets.customImages.containsKey(imageKey)) {
      return config.assets.customImages[imageKey]!;
    }
    return fallback ?? '${_defaultImagePath}placeholder.png';
  }

  /// Get favicon for web builds
  static String getTenantFavicon(TenantConfig config) {
    return config.assets.favicon ?? '${_defaultImagePath}favicon.ico';
  }

  /// Get splash image for app startup
  static String getTenantSplashImage(TenantConfig config) {
    return config.assets.splashImage ?? '${_defaultImagePath}splash.png';
  }

  /// Check if a tenant has a specific image
  static bool hasTenantImage(TenantConfig config, String imageKey) {
    return config.assets.customImages.containsKey(imageKey);
  }

  /// Get all available custom images for a tenant
  static Map<String, String> getAllTenantImages(TenantConfig config) {
    return Map<String, String>.from(config.assets.customImages);
  }
}

/// Service for managing tenant-specific layouts and UI configurations
class TenantLayoutService {
  /// Get the main axis alignment based on layout type
  static MainAxisAlignment getMainAxisAlignment(TenantLayout layout) {
    switch (layout.layoutType) {
      case 'compact':
        return MainAxisAlignment.start;
      case 'sidebar':
        return MainAxisAlignment.spaceBetween;
      case 'cards':
        return MainAxisAlignment.center;
      default:
        return MainAxisAlignment.start;
    }
  }

  /// Get cross axis alignment based on layout type
  static CrossAxisAlignment getCrossAxisAlignment(TenantLayout layout) {
    switch (layout.layoutType) {
      case 'compact':
        return CrossAxisAlignment.stretch;
      case 'sidebar':
        return CrossAxisAlignment.start;
      case 'cards':
        return CrossAxisAlignment.center;
      default:
        return CrossAxisAlignment.start;
    }
  }

  /// Get spacing value for the layout
  static double getSpacing(TenantLayout layout, String spacingKey) {
    if (layout.spacing.containsKey(spacingKey)) {
      return layout.spacing[spacingKey]!.toDouble();
    }

    // Default spacing values
    switch (spacingKey) {
      case 'small':
        return 8.0;
      case 'medium':
        return 16.0;
      case 'large':
        return 24.0;
      default:
        return 16.0;
    }
  }

  /// Get card configuration based on tenant layout
  static Map<String, dynamic> getCardConfig(TenantLayout layout) {
    final config = <String, dynamic>{
      'elevation': 4.0,
      'borderRadius': 12.0,
      'useShadows': false,
    };

    // Apply tenant-specific settings
    if (layout.componentSettings.containsKey('cardElevation')) {
      config['elevation'] =
          layout.componentSettings['cardElevation']?.toDouble() ?? 4.0;
    }
    if (layout.componentSettings.containsKey('borderRadius')) {
      config['borderRadius'] =
          layout.componentSettings['borderRadius']?.toDouble() ?? 12.0;
    }
    if (layout.componentSettings.containsKey('useShadows')) {
      config['useShadows'] = layout.componentSettings['useShadows'] ?? false;
    }

    return config;
  }

  /// Check if a feature should be hidden for this tenant
  static bool isFeatureHidden(TenantLayout layout, String featureName) {
    return layout.hiddenFeatures.contains(featureName);
  }

  /// Get navigation type for the tenant
  static NavigationType getNavigationType(TenantLayout layout) {
    switch (layout.navigationStyle) {
      case 'bottom':
        return NavigationType.bottom;
      case 'rail':
        return NavigationType.rail;
      case 'drawer':
        return NavigationType.drawer;
      default:
        return NavigationType.bottom;
    }
  }

  /// Get app bar configuration
  static AppBarConfig getAppBarConfig(TenantLayout layout) {
    return AppBarConfig(
      style: layout.appBarStyle,
      showTitle: layout.showHeader,
      elevation: layout.appBarStyle == 'prominent' ? 8.0 : 4.0,
    );
  }
}

/// Enum for navigation types
enum NavigationType { bottom, rail, drawer }

/// Configuration class for app bar
class AppBarConfig {
  final String style;
  final bool showTitle;
  final double elevation;

  const AppBarConfig({
    required this.style,
    required this.showTitle,
    required this.elevation,
  });
}

/// Widget that adapts based on tenant layout configuration
class TenantAwareContainer extends StatelessWidget {
  final TenantConfig tenantConfig;
  final Widget child;
  final String? customImageKey;
  final bool useBackground;

  const TenantAwareContainer({
    super.key,
    required this.tenantConfig,
    required this.child,
    this.customImageKey,
    this.useBackground = false,
  });

  @override
  Widget build(BuildContext context) {
    final layout = tenantConfig.layout;
    final spacing = TenantLayoutService.getSpacing(layout, 'medium');

    Widget container = Container(
      padding: EdgeInsets.all(spacing),
      child: child,
    );

    // Add background image if specified
    if (useBackground && customImageKey != null) {
      final imagePath = TenantAssetService.getTenantImage(
        tenantConfig,
        customImageKey!,
      );

      container = Container(
        decoration: BoxDecoration(
          image: DecorationImage(
            image: AssetImage(imagePath),
            fit: BoxFit.cover,
            colorFilter: ColorFilter.mode(
              Colors.black.withOpacity(0.1),
              BlendMode.darken,
            ),
          ),
        ),
        child: container,
      );
    }

    return container;
  }
}

/// Widget for displaying tenant-specific cards
class TenantAwareCard extends StatelessWidget {
  final TenantConfig tenantConfig;
  final Widget child;
  final VoidCallback? onTap;

  const TenantAwareCard({
    super.key,
    required this.tenantConfig,
    required this.child,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final layout = tenantConfig.layout;
    final cardConfig = TenantLayoutService.getCardConfig(layout);

    Widget card;

    switch (layout.cardStyle) {
      case 'outlined':
        card = Card.outlined(child: child);
        break;
      case 'filled':
        card = Card.filled(child: child);
        break;
      default:
        card = Card(elevation: cardConfig['elevation'], child: child);
    }

    if (onTap != null) {
      return InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(cardConfig['borderRadius']),
        child: card,
      );
    }

    return card;
  }
}
