import 'package:dio/dio.dart';
import '../error/exceptions.dart';

class ApiClient {
  final Dio _dio;

  ApiClient(this._dio);

  // Tenant Configuration Endpoints
  Future<Map<String, dynamic>> getTenantConfig(String tenantId) async {
    try {
      final response = await _dio.get('/tenants/$tenantId/config');
      return response.data as Map<String, dynamic>;
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  Future<List<Map<String, dynamic>>> getTenants() async {
    try {
      final response = await _dio.get('/tenants');
      return List<Map<String, dynamic>>.from(response.data);
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  // User Authentication
  Future<Map<String, dynamic>> login(Map<String, dynamic> credentials) async {
    try {
      final response = await _dio.post('/auth/login', data: credentials);
      return response.data as Map<String, dynamic>;
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  Future<Map<String, dynamic>> refreshToken(Map<String, dynamic> token) async {
    try {
      final response = await _dio.post('/auth/refresh', data: token);
      return response.data as Map<String, dynamic>;
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  Future<void> logout() async {
    try {
      await _dio.post('/auth/logout');
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  // Generic API Methods
  Future<Map<String, dynamic>> get(String path) async {
    try {
      final response = await _dio.get(path);
      return response.data as Map<String, dynamic>;
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  Future<Map<String, dynamic>> post(
    String path,
    Map<String, dynamic> data,
  ) async {
    try {
      final response = await _dio.post(path, data: data);
      return response.data as Map<String, dynamic>;
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  Future<Map<String, dynamic>> put(
    String path,
    Map<String, dynamic> data,
  ) async {
    try {
      final response = await _dio.put(path, data: data);
      return response.data as Map<String, dynamic>;
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  Future<void> delete(String path) async {
    try {
      await _dio.delete(path);
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  Exception _handleDioError(DioException error) {
    switch (error.response?.statusCode) {
      case 400:
        return ValidationException(
          error.response?.data['message'] ?? 'Validation error',
        );
      case 401:
        return UnauthorizedException(
          error.response?.data['message'] ?? 'Unauthorized',
        );
      case 404:
        return NotFoundException(
          error.response?.data['message'] ?? 'Not found',
        );
      case 500:
      default:
        return ServerException(
          error.response?.data['message'] ?? 'Server error',
        );
    }
  }
}

// Dio Interceptor for Error Handling
class ApiInterceptor extends Interceptor {
  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    switch (err.response?.statusCode) {
      case 400:
        handler.reject(
          DioException(
            requestOptions: err.requestOptions,
            error: ValidationException(
              err.response?.data['message'] ?? 'Validation error',
            ),
          ),
        );
        break;
      case 401:
        handler.reject(
          DioException(
            requestOptions: err.requestOptions,
            error: UnauthorizedException(
              err.response?.data['message'] ?? 'Unauthorized',
            ),
          ),
        );
        break;
      case 404:
        handler.reject(
          DioException(
            requestOptions: err.requestOptions,
            error: NotFoundException(
              err.response?.data['message'] ?? 'Not found',
            ),
          ),
        );
        break;
      case 500:
      default:
        handler.reject(
          DioException(
            requestOptions: err.requestOptions,
            error: ServerException(
              err.response?.data['message'] ?? 'Server error',
            ),
          ),
        );
        break;
    }
  }
}

// API Client Factory
class ApiClientFactory {
  static ApiClient create({
    required String baseUrl,
    String? accessToken,
    Duration? connectTimeout,
    Duration? receiveTimeout,
  }) {
    final dio = Dio();

    dio.options = BaseOptions(
      baseUrl: baseUrl,
      connectTimeout: connectTimeout ?? const Duration(seconds: 30),
      receiveTimeout: receiveTimeout ?? const Duration(seconds: 30),
      headers: {
        'Content-Type': 'application/json',
        if (accessToken != null) 'Authorization': 'Bearer $accessToken',
      },
    );

    // Add interceptors
    dio.interceptors.addAll([
      ApiInterceptor(),
      LogInterceptor(
        requestBody: true,
        responseBody: true,
        logPrint: (object) {
          // You can integrate with your logging system here
          print(object);
        },
      ),
    ]);

    return ApiClient(dio);
  }
}
