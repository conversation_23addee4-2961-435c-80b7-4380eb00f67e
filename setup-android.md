# Android Development Setup - Step by Step Guide

## Current Status

❌ Android Studio not installed  
❌ Android SDK not available  
❌ No Android Virtual Devices (AVDs) created

## Step 1: Install Android Studio

### Download and Install

1. **Download Android Studio**:

   - Go to: https://developer.android.com/studio
   - Download "Android Studio Ladybug | 2024.3.1" (latest stable)
   - File size: ~1GB

2. **Install Android Studio**:
   - Run the installer as Administrator
   - **IMPORTANT**: Choose "Standard" installation (includes Android SDK)
   - Accept all license agreements
   - Let it download Android SDK components (~3-4GB)

### Post-Installation Setup

3. **First Launch**:

   - Open Android Studio
   - Complete the setup wizard
   - Choose "Standard" setup type
   - Select UI theme (Light/Dark)
   - Let it download SDK components

4. **SDK Location**:
   - Note the SDK location (usually: `C:\Users\<USER>\AppData\Local\Android\Sdk`)
   - We'll use this later

## Step 2: Configure Flutter for Android

### Verify Installation

```bash
# Run this after Android Studio installation
flutter doctor

# Should now show Android toolchain as available
```

### Accept Android Licenses

```bash
# Accept all Android SDK licenses
flutter doctor --android-licenses

# Type 'y' for each license prompt
```

## Step 3: Create Android Virtual Device (AVD)

### Using Android Studio (Recommended)

1. **Open AVD Manager**:

   - Launch Android Studio
   - Click "More Actions" → "AVD Manager"
   - OR: Go to Tools → AVD Manager

2. **Create Virtual Device**:

   - Click "Create Virtual Device"
   - Choose **Phone** category
   - Select **Pixel 7** (recommended for testing)
   - Click "Next"

3. **Select System Image**:

   - Choose **API 34** (Android 14) - Recommended
   - Click "Download" if not already downloaded (~1-2GB)
   - Wait for download to complete
   - Click "Next"

4. **Configure AVD**:
   - **AVD Name**: `Pixel_7_API_34`
   - **Graphics**: Hardware - GLES 2.0
   - **Memory and Storage**:
     - RAM: 2048 MB (or 4096 MB if you have 16GB+ system RAM)
     - VM Heap: 512 MB
     - Internal Storage: 6144 MB
   - Click "Finish"

### Alternative: Command Line Creation

```bash
# List available system images
sdkmanager --list | findstr "system-images"

# Download recommended system image
sdkmanager "system-images;android-34;google_apis;x86_64"

# Create AVD
avdmanager create avd -n Pixel_7_API_34 -k "system-images;android-34;google_apis;x86_64"
```

## Step 4: Test the Setup

### Start Emulator

```bash
# List available emulators
flutter emulators

# Should show: Pixel_7_API_34

# Start the emulator
flutter emulators --launch Pixel_7_API_34
```

### Verify Flutter Detects Android

```bash
# Wait for emulator to fully boot, then check devices
flutter devices

# Should show something like:
# Pixel 7 API 34 (mobile) • emulator-5554 • android-x64 • Android 14 (API 34)
```

### Test Multi-Tenant App

```bash
# Run OCIGM tenant on Android
scripts\run-tenant.bat ocigm android

# Should now work without errors!
```

## Step 5: Test All Tenants

Once the emulator is running, test each tenant:

```bash
# Test Company A
scripts\run-tenant.bat company_a android

# Test Company B
scripts\run-tenant.bat company_b android

# Test OCIGM
scripts\run-tenant.bat ocigm android

# Test Default
scripts\run-tenant.bat default android
```

## Expected Results

### On Android Emulator You'll See:

- ✅ **Bottom Navigation**: Mobile-optimized navigation bar at bottom
- ✅ **Touch-Friendly UI**: Larger buttons and touch targets
- ✅ **Responsive Layout**: Single-column layout optimized for mobile
- ✅ **Tenant Theming**: Colors and branding specific to each tenant
- ✅ **UserInfoCard**: Adapts to mobile screen size

### Performance Notes:

- First boot of emulator takes 2-3 minutes
- App hot reload works on Android emulator
- Use `r` for hot reload, `R` for hot restart

## Troubleshooting

### If `flutter doctor` still shows Android issues:

```bash
# Set Android SDK path manually
flutter config --android-sdk C:\Users\<USER>\AppData\Local\Android\Sdk

# Re-run doctor
flutter doctor
```

### If emulator won't start:

- Ensure Windows Hyper-V is disabled
- Enable virtualization in BIOS
- Try different API level (API 33 instead of 34)
- Restart Android Studio

### If app crashes on Android:

```bash
# Clean and rebuild
flutter clean
flutter pub get
flutter run -d android --dart-define=TENANT_ID=company_a
```

## Next Steps After Setup

1. ✅ Install Android Studio
2. ✅ Create Pixel 7 API 34 emulator
3. ✅ Test `flutter doctor` - should show green checkmarks
4. ✅ Test `flutter emulators` - should list your AVD
5. ✅ Test running multi-tenant app on Android
6. ✅ Compare mobile vs desktop responsive navigation

**Estimated Total Setup Time**: 45-60 minutes (including downloads)
**Required Disk Space**: ~6-8GB total
