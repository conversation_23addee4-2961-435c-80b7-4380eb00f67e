import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import '../../features/tenant/domain/entities/tenant_config.dart';

/// Service to handle responsive navigation layouts for different platforms and screen sizes
class ResponsiveNavigationService {
  /// Breakpoints for responsive design
  static const double mobileBreakpoint = 600;
  static const double tabletBreakpoint = 900;
  static const double desktopBreakpoint = 1200;

  /// Get appropriate navigation style based on screen size and platform
  static NavigationStyle getNavigationStyle(
    BuildContext context,
    TenantConfig tenantConfig,
  ) {
    final size = MediaQuery.of(context).size;
    final width = size.width;
    final platform = Theme.of(context).platform;

    // Check if we're on mobile platform
    final isMobile =
        platform == TargetPlatform.android || platform == TargetPlatform.iOS;

    // Check if we're on web
    final isWeb = kIsWeb;

    // Determine navigation style based on screen size and platform
    if (isMobile || width < mobileBreakpoint) {
      return NavigationStyle.bottomNavigation;
    } else if (width >= desktopBreakpoint || isWeb) {
      return NavigationStyle.sideNavigation;
    } else {
      // Tablet size - use rail navigation
      return NavigationStyle.railNavigation;
    }
  }

  /// Get responsive layout configuration
  static ResponsiveLayoutConfig getLayoutConfig(
    BuildContext context,
    TenantConfig tenantConfig,
  ) {
    final size = MediaQuery.of(context).size;
    final width = size.width;
    final navigationStyle = getNavigationStyle(context, tenantConfig);

    return ResponsiveLayoutConfig(
      navigationStyle: navigationStyle,
      showSidebar: width >= desktopBreakpoint,
      useSingleColumn: width < tabletBreakpoint,
      maxContentWidth: _getMaxContentWidth(width),
      padding: _getPadding(width),
      navigationWidth: _getNavigationWidth(navigationStyle),
    );
  }

  static double _getMaxContentWidth(double screenWidth) {
    if (screenWidth >= desktopBreakpoint) {
      return 1200; // Desktop max width
    } else if (screenWidth >= tabletBreakpoint) {
      return 800; // Tablet max width
    } else {
      return double.infinity; // Mobile uses full width
    }
  }

  static EdgeInsets _getPadding(double screenWidth) {
    if (screenWidth >= desktopBreakpoint) {
      return const EdgeInsets.all(24); // Desktop padding
    } else if (screenWidth >= tabletBreakpoint) {
      return const EdgeInsets.all(16); // Tablet padding
    } else {
      return const EdgeInsets.all(12); // Mobile padding
    }
  }

  static double _getNavigationWidth(NavigationStyle style) {
    switch (style) {
      case NavigationStyle.sideNavigation:
        return 280; // Full sidebar width
      case NavigationStyle.railNavigation:
        return 72; // Rail navigation width
      case NavigationStyle.bottomNavigation:
        return 0; // Bottom navigation takes no horizontal space
    }
  }

  /// Get tenant-specific navigation items
  static List<NavigationItem> getNavigationItems(TenantConfig tenantConfig) {
    final baseItems = [
      NavigationItem(icon: Icons.home, label: 'Home', route: '/'),
      NavigationItem(
        icon: Icons.dashboard,
        label: 'Dashboard',
        route: '/dashboard',
      ),
    ];

    // Add tenant-specific navigation items
    switch (tenantConfig.id) {
      case 'company_a':
        return [
          ...baseItems,
          NavigationItem(
            icon: Icons.business_center,
            label: 'Projects',
            route: '/projects',
          ),
          NavigationItem(
            icon: Icons.people,
            label: 'Clients',
            route: '/clients',
          ),
          NavigationItem(
            icon: Icons.analytics,
            label: 'Reports',
            route: '/reports',
          ),
        ];

      case 'company_b':
        return [
          ...baseItems,
          NavigationItem(
            icon: Icons.eco,
            label: 'Sustainability',
            route: '/sustainability',
          ),
          NavigationItem(
            icon: Icons.nature,
            label: 'Projects',
            route: '/eco-projects',
          ),
          NavigationItem(
            icon: Icons.assessment,
            label: 'Impact',
            route: '/impact',
          ),
        ];

      case 'ocigm':
        return [
          ...baseItems,
          NavigationItem(
            icon: Icons.science,
            label: 'Research',
            route: '/research',
          ),
          NavigationItem(icon: Icons.biotech, label: 'Lab', route: '/lab'),
          NavigationItem(
            icon: Icons.article,
            label: 'Publications',
            route: '/publications',
          ),
        ];

      default:
        return [
          ...baseItems,
          NavigationItem(
            icon: Icons.settings,
            label: 'Settings',
            route: '/settings',
          ),
        ];
    }
  }
}

/// Navigation styles supported by the responsive system
enum NavigationStyle {
  bottomNavigation, // Mobile: Bottom navigation bar
  railNavigation, // Tablet: Navigation rail on the side
  sideNavigation, // Desktop: Full sidebar navigation
}

/// Configuration for responsive layouts
class ResponsiveLayoutConfig {
  final NavigationStyle navigationStyle;
  final bool showSidebar;
  final bool useSingleColumn;
  final double maxContentWidth;
  final EdgeInsets padding;
  final double navigationWidth;

  const ResponsiveLayoutConfig({
    required this.navigationStyle,
    required this.showSidebar,
    required this.useSingleColumn,
    required this.maxContentWidth,
    required this.padding,
    required this.navigationWidth,
  });
}

/// Navigation item model
class NavigationItem {
  final IconData icon;
  final String label;
  final String route;
  final Widget? badge;

  const NavigationItem({
    required this.icon,
    required this.label,
    required this.route,
    this.badge,
  });
}
