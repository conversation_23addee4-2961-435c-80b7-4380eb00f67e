import '../../features/tenant/domain/entities/tenant_config.dart';

/// OCIGM tenant configuration - Premium cards layout
class OCIGMTenantConfig {
  static const TenantConfig config = TenantConfig(
    id: 'ocigm',
    name: 'ocigm',
    displayName: 'OCIGM',
    theme: TenantTheme(
      primaryColors: TenantColorScheme(
        primary: '#9C27B0',
        secondary: '#FF9800',
        surface: '#FFFFFF',
        background: '#F3E5F5',
        error: '#F44336',
        onPrimary: '#FFFFFF',
        onSecondary: '#000000',
        onSurface: '#000000',
        onBackground: '#000000',
        onError: '#FFFFFF',
      ),
      darkColors: TenantColorScheme(
        primary: '#CE93D8',
        secondary: '#FFB74D',
        surface: '#2C1B47',
        background: '#1A0E2E',
        error: '#CF6679',
        onPrimary: '#000000',
        onSecondary: '#000000',
        onSurface: '#FFFFFF',
        onBackground: '#FFFFFF',
        onError: '#000000',
      ),
      fontFamily: 'Inter',
    ),
    assets: TenantAssets(
      logo: 'assets/images/tenants/ocigm/ocigm_logo.png',
      logoLight: 'assets/images/tenants/ocigm/ocigm_logo_light.png',
      logoDark: 'assets/images/tenants/ocigm/ocigm_logo_dark.png',
      customImages: {
        'hero_banner': 'assets/images/tenants/ocigm/premium_banner.jpg',
        'feature_image': 'assets/images/tenants/ocigm/innovation_feature.jpg',
        'background_pattern': 'assets/images/tenants/ocigm/purple_pattern.png',
        'innovation_lab': 'assets/images/tenants/ocigm/innovation_lab.jpg',
        'premium_workspace':
            'assets/images/tenants/ocigm/premium_workspace.jpg',
      },
    ),
    layout: TenantLayout(
      layoutType: 'cards',
      showHeader: true,
      showSidebar: false,
      showFooter: true,
      appBarStyle: 'prominent',
      navigationStyle: 'bottom',
      cardStyle: 'elevated',
      componentSettings: {
        'cardElevation': 8,
        'borderRadius': 16,
        'useShadows': true,
        'premiumAnimations': true,
        'gradientBackgrounds': true,
      },
      hiddenFeatures: [],
      spacing: {'small': 16, 'medium': 24, 'large': 32},
    ),
    customSettings: {
      'companyType': 'premium',
      'industry': 'technology',
      'tier': 'enterprise',
      'showBranding': true,
      'customWelcomeMessage': 'Welcome to OCIGM Innovation Hub!',
      'enableAdvancedFeatures': true,
      'theme': 'premium',
      'supportEmail': '<EMAIL>',
      'features': [
        'premium_features',
        'cards_ui',
        'advanced_analytics',
        'ai_integration',
        'custom_workflows',
      ],
      'brandColors': ['#9C27B0', '#FF9800'],
      'motto': 'Innovation Through Technology',
      'premiumFeatures': true,
    },
  );
}
