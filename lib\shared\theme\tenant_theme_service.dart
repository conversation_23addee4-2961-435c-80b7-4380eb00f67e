import 'package:flutter/material.dart';
import '../../features/tenant/domain/entities/tenant_config.dart';

class TenantThemeService {
  static ThemeData createThemeFromTenantConfig(
    TenantConfig tenantConfig, {
    bool isDarkMode = false,
  }) {
    final colorScheme = isDarkMode
        ? tenantConfig.theme.darkColors ?? tenantConfig.theme.primaryColors
        : tenantConfig.theme.primaryColors;

    return ThemeData(
      useMaterial3: true,
      colorScheme: _createColorScheme(colorScheme, isDarkMode),
      fontFamily: tenantConfig.theme.fontFamily,
      elevatedButtonTheme: _createElevatedButtonTheme(colorScheme),
      appBarTheme: _createAppBarTheme(colorScheme),
      cardTheme: _createCardTheme(),
      inputDecorationTheme: _createInputDecorationTheme(colorScheme),
    );
  }

  static ColorScheme _createColorScheme(
    TenantColorScheme tenantColors,
    bool isDarkMode,
  ) {
    final primary = _parseColor(tenantColors.primary) ?? Colors.blue;
    final secondary = _parseColor(tenantColors.secondary) ?? primary;
    final surface = _parseColor(tenantColors.surface);
    final background = _parseColor(tenantColors.background);
    final error = _parseColor(tenantColors.error) ?? Colors.red;

    if (isDarkMode) {
      return ColorScheme.dark(
        primary: primary,
        secondary: secondary,
        surface: surface ?? const Color(0xFF121212),
        error: error,
        onPrimary: _parseColor(tenantColors.onPrimary) ?? Colors.white,
        onSecondary: _parseColor(tenantColors.onSecondary) ?? Colors.white,
        onSurface: _parseColor(tenantColors.onSurface) ?? Colors.white,
        onError: _parseColor(tenantColors.onError) ?? Colors.white,
      );
    } else {
      return ColorScheme.light(
        primary: primary,
        secondary: secondary,
        surface: surface ?? Colors.white,
        error: error,
        onPrimary: _parseColor(tenantColors.onPrimary) ?? Colors.white,
        onSecondary: _parseColor(tenantColors.onSecondary) ?? Colors.white,
        onSurface: _parseColor(tenantColors.onSurface) ?? Colors.black,
        onError: _parseColor(tenantColors.onError) ?? Colors.white,
      );
    }
  }

  static Color? _parseColor(String? colorString) {
    if (colorString == null) return null;

    // Remove # if present
    String hexColor = colorString.replaceAll('#', '');

    // Add alpha if not present
    if (hexColor.length == 6) {
      hexColor = 'FF$hexColor';
    }

    try {
      return Color(int.parse(hexColor, radix: 16));
    } catch (e) {
      return null; // Return null on parse error
    }
  }

  static ElevatedButtonThemeData _createElevatedButtonTheme(
    TenantColorScheme colorScheme,
  ) {
    return ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        elevation: 2,
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    );
  }

  static AppBarTheme _createAppBarTheme(TenantColorScheme colorScheme) {
    return AppBarTheme(
      elevation: 1,
      centerTitle: true,
      titleTextStyle: TextStyle(
        fontSize: 20,
        fontWeight: FontWeight.w600,
        color: _parseColor(colorScheme.onPrimary),
      ),
    );
  }

  static CardThemeData _createCardTheme() {
    return const CardThemeData(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.all(Radius.circular(12)),
      ),
      margin: EdgeInsets.all(8),
    );
  }

  static InputDecorationTheme _createInputDecorationTheme(
    TenantColorScheme colorScheme,
  ) {
    return InputDecorationTheme(
      border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
    );
  }

  // Helper method to get asset path based on tenant
  static String getTenantAssetPath(String assetName, String tenantId) {
    return 'assets/images/tenants/$tenantId/$assetName';
  }

  // Helper method to get fallback asset path
  static String getDefaultAssetPath(String assetName) {
    return 'assets/images/$assetName';
  }
}
