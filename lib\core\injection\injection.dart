import 'package:get_it/get_it.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:dio/dio.dart';
import 'package:hive_flutter/hive_flutter.dart';

import '../storage/storage_service.dart';
import '../network/api_client.dart';
import '../../features/tenant/presentation/bloc/tenant_bloc.dart';

final getIt = GetIt.instance;

Future<void> configureDependencies() async {
  // Initialize Hive
  await Hive.initFlutter();

  // Register external dependencies
  final sharedPreferences = await SharedPreferences.getInstance();
  getIt.registerSingleton<SharedPreferences>(sharedPreferences);

  final dio = Dio();
  dio.options.baseUrl = 'https://api.example.com/';
  dio.options.connectTimeout = const Duration(seconds: 30);
  dio.options.receiveTimeout = const Duration(seconds: 30);

  // Add interceptors
  dio.interceptors.addAll([
    ApiInterceptor(),
    LogInterceptor(
      requestBody: true,
      responseBody: true,
      logPrint: (object) {
        // You can integrate with your logging system here
        print(object);
      },
    ),
  ]);

  getIt.registerSingleton<Dio>(dio);

  // Register core services
  getIt.registerLazySingleton<StorageService>(
    () => StorageService(getIt<SharedPreferences>()),
  );

  getIt.registerLazySingleton<ApiClient>(() => ApiClient(getIt<Dio>()));

  // Register BLoCs
  getIt.registerFactory<TenantBloc>(() => TenantBloc());
}
