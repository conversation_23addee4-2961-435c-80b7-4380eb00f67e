# UserInfoCard Component Usage Guide

The `UserInfoCard` is a common component that can be used by all tenants to display user information in a consistent way while respecting tenant-specific theming and layout preferences.

## Basic Usage

### Import the component

```dart
import 'package:papp/shared/widgets/user_info_card.dart';
```

### 1. Basic UserInfoCard

```dart
UserInfoCard(
  tenantConfig: config,
  userName: '<PERSON>',
  userEmail: '<EMAIL>',
  userRole: 'Project Manager',
  userDepartment: 'Engineering',
  onEditProfile: () {
    // Handle edit profile action
  },
)
```

### 2. Compact UserInfoCard

```dart
UserInfoCard(
  tenantConfig: config,
  userName: '<PERSON>',
  userEmail: '<EMAIL>',
  userRole: 'Designer',
  isCompact: true,
  showActions: false,
)
```

### 3. UserInfoCard with Avatar

```dart
UserInfoCard(
  tenantConfig: config,
  userName: '<PERSON>',
  userEmail: '<EMAIL>',
  userRole: 'Team Lead',
  avatarUrl: 'https://example.com/avatar.jpg',
  onTap: () {
    // Handle card tap
  },
  onEditProfile: () {
    // Handle edit action
  },
)
```

### 4. CurrentUserInfoCard (Preset)

For displaying the current logged-in user with tenant-specific mock data:

```dart
CurrentUserInfoCard(
  tenantConfig: config,
  isCompact: false,
  onProfileTap: () {
    // Navigate to profile page
  },
  onSettingsTap: () {
    // Navigate to settings page
  },
)
```

## Component Properties

### UserInfoCard Properties

| Property         | Type            | Required | Description                                    |
| ---------------- | --------------- | -------- | ---------------------------------------------- |
| `tenantConfig`   | `TenantConfig`  | ✅       | Tenant configuration for theming               |
| `userName`       | `String`        | ✅       | User's display name                            |
| `userEmail`      | `String`        | ✅       | User's email address                           |
| `userRole`       | `String?`       | ❌       | User's role/title                              |
| `userDepartment` | `String?`       | ❌       | User's department                              |
| `avatarUrl`      | `String?`       | ❌       | User's avatar image URL                        |
| `onTap`          | `VoidCallback?` | ❌       | Callback when card is tapped                   |
| `onEditProfile`  | `VoidCallback?` | ❌       | Callback for edit profile action               |
| `isCompact`      | `bool`          | ❌       | Whether to use compact layout (default: false) |
| `showActions`    | `bool`          | ❌       | Whether to show action buttons (default: true) |

### CurrentUserInfoCard Properties

| Property        | Type            | Required | Description                                    |
| --------------- | --------------- | -------- | ---------------------------------------------- |
| `tenantConfig`  | `TenantConfig`  | ✅       | Tenant configuration for theming               |
| `onProfileTap`  | `VoidCallback?` | ❌       | Callback when profile is tapped                |
| `onSettingsTap` | `VoidCallback?` | ❌       | Callback when settings is tapped               |
| `isCompact`     | `bool`          | ❌       | Whether to use compact layout (default: false) |

## Tenant-Specific Examples

### Company A (Business Focus)

```dart
CompanyAWidgets.buildUserProfile(context, config, isCompact: false)
```

Shows business-focused user info with Project Manager role.

### OCIGM (Research Focus)

```dart
OCIGMWidgets.buildUserProfile(context, config, isCompact: true)
```

Shows research-focused user info with Research Director role.

## Features

### ✅ Tenant Theming

- Automatically uses tenant-specific primary colors
- Adapts to tenant theme settings
- Respects tenant layout preferences

### ✅ Responsive Design

- Standard layout for full display
- Compact layout for sidebars/small spaces
- Mobile-friendly touch targets

### ✅ Fallback Handling

- Shows initials if no avatar URL provided
- Graceful handling of missing user data
- Error handling for failed avatar loading

### ✅ Accessibility

- Proper semantic structure
- Touch target sizes meet accessibility guidelines
- Screen reader friendly

## Layout Variations

### Standard Layout

- Large avatar (56x56)
- Full user details
- Department information
- Action button with label

### Compact Layout

- Small avatar (40x40)
- Essential user info only
- Horizontal layout
- Minimal action button

## Integration with Tenant Widgets

You can create tenant-specific wrapper methods:

```dart
// In company_a_widgets.dart
static Widget buildUserProfile(BuildContext context, TenantConfig config) {
  return CurrentUserInfoCard(
    tenantConfig: config,
    onProfileTap: () => Navigator.pushNamed(context, '/profile'),
    onSettingsTap: () => Navigator.pushNamed(context, '/settings'),
  );
}
```

This allows each tenant to customize the behavior while using the common component structure.
