import 'package:flutter/material.dart';
import '../../features/tenant/domain/entities/tenant_config.dart';
import '../../shared/widgets/user_info_card.dart';

/// Premium custom widgets specific to OCIGM tenant
class OCIGMWidgets {
  /// OCIGM premium welcome card with animations
  static Widget buildPremiumWelcome(BuildContext context, TenantConfig config) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            _parseColor(config.theme.primaryColors.primary),
            _parseColor(
              config.theme.primaryColors.secondary ??
                  config.theme.primaryColors.primary,
            ),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: _parseColor(
              config.theme.primaryColors.primary,
            ).withOpacity(0.3),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              shape: BoxShape.circle,
            ),
            child: Icon(Icons.diamond, size: 48, color: Colors.white),
          ),
          const SizedBox(height: 16),
          Text(
            config.displayName,
            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            config.customSettings['customWelcomeMessage'] ??
                'Welcome to Premium Experience',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: Colors.white.withOpacity(0.9),
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          Text(
            config.customSettings['motto'] ?? 'Innovation Through Technology',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.white.withOpacity(0.8),
              fontStyle: FontStyle.italic,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// Premium innovation dashboard
  static Widget buildInnovationDashboard(
    BuildContext context,
    TenantConfig config,
  ) {
    return Column(
      children: [
        _buildInnovationMetrics(context, config),
        const SizedBox(height: 16),
        _buildInnovationProjects(context, config),
      ],
    );
  }

  /// OCIGM user profile with research credentials
  static Widget buildUserProfile(
    BuildContext context,
    TenantConfig config, {
    bool isCompact = false,
  }) {
    return CurrentUserInfoCard(
      tenantConfig: config,
      isCompact: isCompact,
      onProfileTap: () => _showFeatureDialog(
        context,
        'Research Profile',
        'View your research credentials and publications',
      ),
      onSettingsTap: () => _showFeatureDialog(
        context,
        'Lab Settings',
        'Configure your research environment and preferences',
      ),
    );
  }

  /// OCIGM premium action grid
  static Widget buildPremiumActions(BuildContext context, TenantConfig config) {
    final primaryColor = _parseColor(config.theme.primaryColors.primary);
    final secondaryColor = _parseColor(
      config.theme.primaryColors.secondary ??
          config.theme.primaryColors.primary,
    );

    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: 2,
      mainAxisSpacing: 16,
      crossAxisSpacing: 16,
      childAspectRatio: 1.2,
      children: [
        _buildPremiumActionCard(
          context,
          'AI Analytics',
          Icons.psychology,
          primaryColor,
          () => _showFeatureDialog(
            context,
            'AI Analytics',
            'Advanced AI-powered business analytics',
          ),
        ),
        _buildPremiumActionCard(
          context,
          'Innovation Lab',
          Icons.science,
          secondaryColor,
          () => _showFeatureDialog(
            context,
            'Innovation Lab',
            'Research and development workspace',
          ),
        ),
        _buildPremiumActionCard(
          context,
          'Premium Support',
          Icons.support_agent,
          primaryColor,
          () => _showFeatureDialog(
            context,
            'Premium Support',
            '24/7 dedicated premium support',
          ),
        ),
        _buildPremiumActionCard(
          context,
          'Custom Workflows',
          Icons.alt_route,
          secondaryColor,
          () => _showFeatureDialog(
            context,
            'Custom Workflows',
            'Build custom business workflows',
          ),
        ),
      ],
    );
  }

  /// OCIGM specific home page content
  static Widget buildHomePage(BuildContext context, TenantConfig config) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // User info section
          CurrentUserInfoCard(
            tenantConfig: config,
            isCompact: false,
            onProfileTap: () => _showFeatureDialog(
              context,
              'Researcher Profile',
              'View and edit your research profile and credentials',
            ),
            onSettingsTap: () => _showFeatureDialog(
              context,
              'Lab Settings',
              'Manage your laboratory and research settings',
            ),
          ),
          const SizedBox(height: 24),

          // Innovation Welcome Section
          _buildInnovationWelcomeSection(context, config),
          const SizedBox(height: 24),

          // Research Dashboard
          _buildResearchDashboard(context, config),
          const SizedBox(height: 24),

          // Recent Publications
          _buildRecentPublications(context, config),
          const SizedBox(height: 24),

          // Innovation Lab Actions
          _buildInnovationActions(context, config),
        ],
      ),
    );
  }

  /// Innovation-specific welcome section
  static Widget _buildInnovationWelcomeSection(
    BuildContext context,
    TenantConfig config,
  ) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            _parseColor(config.theme.primaryColors.primary),
            _parseColor(
              config.theme.primaryColors.secondary ??
                  config.theme.primaryColors.primary,
            ),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: _parseColor(
              config.theme.primaryColors.primary,
            ).withOpacity(0.3),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              shape: BoxShape.circle,
            ),
            child: Icon(Icons.science, size: 48, color: Colors.white),
          ),
          const SizedBox(height: 16),
          Text(
            'Welcome to ${config.displayName}',
            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            'Innovation Through Research Excellence',
            style: Theme.of(
              context,
            ).textTheme.titleMedium?.copyWith(color: Colors.white70),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          Text(
            'Advancing scientific knowledge through cutting-edge research and innovative solutions.',
            style: Theme.of(
              context,
            ).textTheme.bodyLarge?.copyWith(color: Colors.white),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// Research dashboard overview
  static Widget _buildResearchDashboard(
    BuildContext context,
    TenantConfig config,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Research Overview',
          style: Theme.of(
            context,
          ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: MediaQuery.of(context).size.width > 600 ? 4 : 2,
          mainAxisSpacing: 16,
          crossAxisSpacing: 16,
          childAspectRatio: 1.2,
          children: [
            _buildResearchMetricCard(
              context,
              'Active Projects',
              '12',
              Icons.science,
              config,
              Colors.blue,
            ),
            _buildResearchMetricCard(
              context,
              'Publications',
              '45',
              Icons.article,
              config,
              Colors.green,
            ),
            _buildResearchMetricCard(
              context,
              'Citations',
              '1,234',
              Icons.format_quote,
              config,
              Colors.orange,
            ),
            _buildResearchMetricCard(
              context,
              'Collaborators',
              '28',
              Icons.group_work,
              config,
              Colors.purple,
            ),
          ],
        ),
      ],
    );
  }

  /// Recent publications section
  static Widget _buildRecentPublications(
    BuildContext context,
    TenantConfig config,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Recent Publications',
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
            ),
            TextButton(
              onPressed: () => _showFeatureDialog(
                context,
                'All Publications',
                'View all your research publications',
              ),
              child: const Text('View All'),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Card(
          child: ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: 3,
            separatorBuilder: (context, index) => const Divider(),
            itemBuilder: (context, index) {
              final publications = [
                {
                  'title': 'Advanced AI in Healthcare Applications',
                  'journal': 'Nature Medicine',
                  'date': '2025-01-15',
                  'citations': '23',
                },
                {
                  'title': 'Quantum Computing Breakthroughs',
                  'journal': 'Science',
                  'date': '2024-12-20',
                  'citations': '67',
                },
                {
                  'title': 'Sustainable Energy Solutions',
                  'journal': 'Cell Reports',
                  'date': '2024-11-10',
                  'citations': '89',
                },
              ];

              final publication = publications[index];
              return ListTile(
                leading: CircleAvatar(
                  backgroundColor: _parseColor(
                    config.theme.primaryColors.primary,
                  ),
                  child: Icon(Icons.article, color: Colors.white),
                ),
                title: Text(
                  publication['title'] as String,
                  style: const TextStyle(fontWeight: FontWeight.w500),
                ),
                subtitle: Text(
                  '${publication['journal']} �?${publication['date']} �?${publication['citations']} citations',
                ),
                trailing: Icon(
                  Icons.open_in_new,
                  color: _parseColor(config.theme.primaryColors.primary),
                ),
                onTap: () => _showFeatureDialog(
                  context,
                  publication['title'] as String,
                  'View publication details and metrics',
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  /// Innovation lab actions
  static Widget _buildInnovationActions(
    BuildContext context,
    TenantConfig config,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Lab Actions',
          style: Theme.of(
            context,
          ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        Wrap(
          spacing: 12,
          runSpacing: 12,
          children: [
            _buildInnovationActionButton(
              context,
              'New Research',
              Icons.science,
              config,
              () => _showFeatureDialog(
                context,
                'New Research Project',
                'Start a new research initiative',
              ),
            ),
            _buildInnovationActionButton(
              context,
              'Lab Equipment',
              Icons.biotech,
              config,
              () => _showFeatureDialog(
                context,
                'Lab Equipment',
                'Manage laboratory equipment and resources',
              ),
            ),
            _buildInnovationActionButton(
              context,
              'Data Analysis',
              Icons.analytics,
              config,
              () => _showFeatureDialog(
                context,
                'Data Analysis',
                'Access research data analysis tools',
              ),
            ),
            _buildInnovationActionButton(
              context,
              'Collaborate',
              Icons.group_work,
              config,
              () => _showFeatureDialog(
                context,
                'Collaboration',
                'Connect with research collaborators',
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// Build research metric card
  static Widget _buildResearchMetricCard(
    BuildContext context,
    String title,
    String value,
    IconData icon,
    TenantConfig config,
    Color color,
  ) {
    return Card(
      elevation: 4,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          gradient: LinearGradient(
            colors: [color.withOpacity(0.1), color.withOpacity(0.05)],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, size: 32, color: color),
            const SizedBox(height: 8),
            Text(
              value,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            Text(
              title,
              style: Theme.of(context).textTheme.bodySmall,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// Build innovation action button
  static Widget _buildInnovationActionButton(
    BuildContext context,
    String label,
    IconData icon,
    TenantConfig config,
    VoidCallback onPressed,
  ) {
    return ElevatedButton.icon(
      onPressed: onPressed,
      icon: Icon(icon, size: 20),
      label: Text(label),
      style: ElevatedButton.styleFrom(
        backgroundColor: _parseColor(config.theme.primaryColors.primary),
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        elevation: 4,
        shadowColor: _parseColor(
          config.theme.primaryColors.primary,
        ).withOpacity(0.3),
      ),
    );
  }

  static Color _parseColor(String colorString) {
    return Color(int.parse(colorString.replaceFirst('#', '0xFF')));
  }

  static void _showFeatureDialog(
    BuildContext context,
    String title,
    String description,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(description),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  static Widget _buildInnovationMetrics(
    BuildContext context,
    TenantConfig config,
  ) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Research Metrics',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: _parseColor(config.theme.primaryColors.primary),
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildMetricCard(
                    context,
                    'Active Studies',
                    '12',
                    Icons.science,
                    config,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildMetricCard(
                    context,
                    'Publications',
                    '47',
                    Icons.article,
                    config,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildMetricCard(
                    context,
                    'Citations',
                    '1,284',
                    Icons.format_quote,
                    config,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  static Widget _buildMetricCard(
    BuildContext context,
    String title,
    String value,
    IconData icon,
    TenantConfig config,
  ) {
    return Card(
      elevation: 2,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          gradient: LinearGradient(
            colors: [
              _parseColor(config.theme.primaryColors.primary).withOpacity(0.1),
              _parseColor(config.theme.primaryColors.primary).withOpacity(0.05),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: 32,
              color: _parseColor(config.theme.primaryColors.primary),
            ),
            const SizedBox(height: 8),
            Text(
              value,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: _parseColor(config.theme.primaryColors.primary),
              ),
            ),
            Text(
              title,
              style: Theme.of(context).textTheme.bodySmall,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// OCIGM specific projects overview
  static Widget _buildInnovationProjects(
    BuildContext context,
    TenantConfig config,
  ) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Current Research Projects',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: _parseColor(config.theme.primaryColors.primary),
              ),
            ),
            const SizedBox(height: 16),
            _buildProjectItem(
              context,
              'AI-Driven Drug Discovery',
              'Phase II clinical trials',
              Icons.biotech,
              config,
            ),
            const SizedBox(height: 8),
            _buildProjectItem(
              context,
              'Quantum Computing Applications',
              'Algorithm development',
              Icons.computer,
              config,
            ),
            const SizedBox(height: 8),
            _buildProjectItem(
              context,
              'Sustainable Materials Research',
              'Lab testing phase',
              Icons.eco,
              config,
            ),
          ],
        ),
      ),
    );
  }

  static Widget _buildProjectItem(
    BuildContext context,
    String title,
    String status,
    IconData icon,
    TenantConfig config,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: _parseColor(
          config.theme.primaryColors.primary,
        ).withOpacity(0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: _parseColor(
            config.theme.primaryColors.primary,
          ).withOpacity(0.2),
        ),
      ),
      child: Row(
        children: [
          Icon(
            icon,
            color: _parseColor(config.theme.primaryColors.primary),
            size: 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: Theme.of(
                    context,
                  ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w600),
                ),
                Text(
                  status,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
          Icon(
            Icons.arrow_forward_ios,
            size: 16,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
        ],
      ),
    );
  }

  /// Build premium action card
  static Widget _buildPremiumActionCard(
    BuildContext context,
    String title,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return Card(
      elevation: 1,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(icon, color: color, size: 24),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Text(
                  title,
                  style: Theme.of(
                    context,
                  ).textTheme.bodyLarge?.copyWith(fontWeight: FontWeight.w600),
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
